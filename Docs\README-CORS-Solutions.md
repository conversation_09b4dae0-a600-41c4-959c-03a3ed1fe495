# OpenSearch CORS Solutions

## The Problem

You're experiencing CORS errors because:

1. **OpenSearch JavaScript Client is NOT designed for browser use** - The official client is meant for server-side Node.js applications
2. **Browser Security Policy** - Browsers block cross-origin requests by default
3. **OpenSearch Server Configuration** - Your server likely doesn't have CORS headers configured

## Solutions (Ranked by Recommendation)

### 🎯 Solution 1: API Gateway (RECOMMENDED)

**Pros:**
- ✅ Uses official OpenSearch JavaScript client
- ✅ Most secure approach
- ✅ No server configuration needed
- ✅ Works with any OpenSearch setup
- ✅ Hides credentials from browser
- ✅ Production-ready with PM2

**Setup:**

**Windows:**
1. Run the setup script: `setup-proxy.bat`

**Linux/RHEL:**
1. Run the setup script: `./setup-gateway.sh`
2. Or install as service: `sudo ./install-service.sh`

**Manual Setup:**
```bash
cd server
npm install
npm start
```

**Access:** `http://localhost:3001`

**Enable in app:**
```javascript
opensearchClient.configureGateway(true, 'http://localhost:3001');
```

### 🔧 Solution 2: OpenSearch Server CORS Configuration

**Pros:**
- ✅ Direct browser connection
- ✅ No proxy needed

**Cons:**
- ❌ Requires admin access to OpenSearch server
- ❌ Security risks (credentials exposed to browser)
- ❌ May not be allowed in production

**Configuration:**
Ask your OpenSearch administrator to add to `opensearch.yml`:
```yaml
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-headers: "X-Requested-With,Content-Type,Content-Length,Authorization"
http.cors.allow-credentials: true
http.cors.allow-methods: "OPTIONS,HEAD,GET,POST,PUT,DELETE"
```

### 🌐 Solution 3: Browser Extensions (DEVELOPMENT ONLY)

**Pros:**
- ✅ Quick for testing

**Cons:**
- ❌ Only for development
- ❌ Security risks
- ❌ Not reliable across browsers

**Options:**
- Install "CORS Unblock" browser extension
- Run Chrome with disabled security:
  ```bash
  chrome.exe --user-data-dir="C:/Chrome dev" --disable-web-security --disable-features=VizDisplayCompositor
  ```

## What the Official Documentation Says

According to OpenSearch documentation:

> "The OpenSearch JavaScript (JS) client provides a safer and easier way to interact with your OpenSearch cluster. **Rather than using OpenSearch from the browser** and potentially exposing your data to the public, you can build an OpenSearch client that takes care of sending requests to your cluster."

**Key Points:**
- ❌ Browser usage is **NOT recommended** by OpenSearch
- ✅ Server-side usage is the **intended approach**
- 🔒 Security concerns with browser-based implementations

## Recommended Implementation

1. **Use the API Gateway** (Solution 1)
2. **Keep credentials on server-side**
3. **Use HTTPS in production**
4. **Implement proper authentication**

## Testing Your Setup

1. Start the API Gateway server
2. Open `http://localhost:3001`
3. Switch to OpenSearch mode
4. Configure with gateway enabled
5. Test connection

The API Gateway uses the official OpenSearch JavaScript client and handles all CORS issues while providing a secure bridge between your browser and OpenSearch.

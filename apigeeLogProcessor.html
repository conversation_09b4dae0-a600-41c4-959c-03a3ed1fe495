<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apigee Log Processor</title>    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="apigeeLogProcessor.css">
</head>
<body>
    <!-- Theme Switcher -->
    <div class="theme-switch-wrapper">
        <label class="theme-switch" for="themeSwitch">
            <input type="checkbox" id="themeSwitch" />
            <div class="slider">
                <i class="bi bi-sun-fill slider-icon sun"></i>
                <i class="bi bi-moon-fill slider-icon moon"></i>
            </div>
        </label>
    </div>

    <div class="container" id="inputContainer">
        <div class="d-flex justify-content-between align-items-center">
            <!-- Mode Switcher on the left -->
            <div class="d-flex align-items-center gap-1">
                <label class="theme-switch" for="modeSwitch">
                    <input type="checkbox" id="modeSwitch" checked />
                    <div class="slider">
                        <i class="bi bi-file-earmark-text slider-icon sun"></i>
                        <i class="bi bi-search slider-icon moon"></i>
                    </div>
                </label>
                <!-- OpenSearch Config and Status -->
                <button id="opensearchConfigButton" class="header-config-btn" title="Configure OpenSearch">
                    <i class="bi bi-gear"></i>
                </button>
                <span id="opensearchStatus" class="header-status-badge">Not Connected</span>
            </div>
            <!-- Centered title -->
            <h2 class="text-center flex-grow-1">Apigee Log Processor</h2>
            <!-- Toggle button on the right -->
            <button class="btn btn-sm btn-outline-secondary d-none" id="toggleInputButton" title="Expand input section">
                <i class="bi bi-chevron-down"></i>
            </button>
        </div>

        <div class="input-section" id="inputSection">

            <!-- File Mode Section -->
            <div id="fileModeSection" class="hidden">
                <h4 class="mb-3">Paste text or Open from file</h4>
                <div class="mb-3">
                    <textarea id="textInput" class="form-control shadow-sm" rows="10" cols="100" placeholder="Paste your log content here..."></textarea>
                </div>
                <div class="mb-3">
                    <input type="file" id="fileInput" class="form-control shadow-sm" accept=".txt,.log,.json,.csv">
                </div>
                <div class="d-flex mt-3 mb-2">
                    <button class="btn btn-primary" id="processButton">
                        <i class="bi bi-diagram-3 me-1"></i> Process Logs
                    </button>
                    <button class="btn btn-warning" id="extractButton">
                        <i class="bi bi-filter me-1"></i> Extract message Id
                    </button>
                    <button class="btn btn-danger" id="clearButton">
                        <i class="bi bi-x-lg me-1"></i> Clear All
                    </button>
                </div>
            </div>

            <!-- OpenSearch Mode Section -->
            <div id="opensearchModeSection">
                <!-- Configuration Row -->
                <div class="mb-3 d-flex gap-2 align-items-center">
                    <select id="opensearchIndexPattern" class="form-select" style="max-width: 200px;">
                        <option value="okd-test*">okd-test*</option>
                        <option value="okd-prod*">okd-prod*</option>
                        <option value="apigee-e2e*">apigee-e2e*</option>
                        <option value="apigee-sit*">apigee-sit*</option>
                        <option value="apigee-uat-pet-*">apigee-uat-pet-*</option>
                        <option value="apigee-prod-*" selected>apigee-prod-*</option>
                    </select>
                    <!-- OpenSearch-style Time Range with From/To fields -->
                    <div class="opensearch-time-range d-flex align-items-center gap-2">
                        <i class="bi bi-calendar3"></i>

                        <!-- From Field -->
                        <div class="time-field-container" style="position: relative;">
                            <button class="btn btn-outline-secondary time-field-btn" type="button" id="fromTimeButton">
                                <span id="fromTimeDisplay">Jul 19, 2025 @ 14:45:40.033</span>
                                <i class="bi bi-chevron-down ms-2"></i>
                            </button>
                            <div class="time-dropdown-menu" id="fromTimeMenu" style="display: none;">
                                <!-- Tabs -->
                                <div class="time-tabs">
                                    <button class="time-tab active" data-tab="absolute" data-field="from">Absolute</button>
                                    <button class="time-tab" data-tab="relative" data-field="from">Relative</button>
                                </div>

                                <!-- Tab Content -->
                                <div class="time-tab-content">
                                    <!-- Absolute Tab -->
                                    <div class="tab-pane active" id="from-absolute">
                                        <div class="absolute-datetime-picker">
                                            <div class="row g-2 mb-3">
                                                <div class="col-6">
                                                    <label class="form-label small">Date</label>
                                                    <input type="date" class="form-control form-control-sm" id="fromDatePicker">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label small">Time</label>
                                                    <input type="time" class="form-control form-control-sm" id="fromTimePicker" step="1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="start-date-display">
                                            <strong>Start date:</strong> <span id="fromDateDisplay">Jul 19, 2025 @ 14:45:40.033</span>
                                        </div>
                                        <div class="d-flex justify-content-end">
                                            <button class="btn btn-primary btn-sm" id="fromSetAbsolute">Set</button>
                                        </div>
                                    </div>

                                    <!-- Relative Tab -->
                                    <div class="tab-pane" id="from-relative">
                                        <div class="relative-inputs">
                                            <input type="number" class="form-control" id="fromRelativeValue" value="15" min="1">
                                            <select class="form-select" id="fromRelativeUnit">
                                                <option value="minutes">Minutes ago</option>
                                                <option value="hours">Hours ago</option>
                                                <option value="days">Days ago</option>
                                                <option value="weeks">Weeks ago</option>
                                                <option value="months">Months ago</option>
                                            </select>
                                        </div>
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="fromRoundToMinute">
                                            <label class="form-check-label" for="fromRoundToMinute">Round to the minute</label>
                                        </div>

                                        <div class="start-date-display">
                                            <strong>Start date:</strong> <span id="fromRelativeDisplay">Jan 24, 2023 @ 12:44:31.567</span>
                                        </div>
                                        <div class="d-flex justify-content-end">
                                            <button class="btn btn-primary btn-sm" id="fromSetRelative">Set</button>
                                        </div>

                                        <!-- Commonly Used Options -->
                                        <div class="commonly-used-section mt-3">
                                            <h6 class="small text-muted mb-2">Commonly Used</h6>
                                            <div class="commonly-used-grid">
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="0" data-unit="minutes" data-label="Today">Today</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="0" data-unit="week" data-label="This week">This week</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="15" data-unit="minutes" data-label="Last 15 minutes">Last 15 minutes</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="30" data-unit="minutes" data-label="Last 30 minutes">Last 30 minutes</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="1" data-unit="hours" data-label="Last 1 hour">Last 1 hour</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="24" data-unit="hours" data-label="Last 24 hours">Last 24 hours</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="7" data-unit="days" data-label="Last 7 days">Last 7 days</button>
                                                <button class="btn btn-outline-secondary btn-sm commonly-used-btn" data-value="15" data-unit="days" data-label="Last 15 days">Last 15 days</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <span class="text-muted">→</span>

                        <!-- To Field -->
                        <div class="time-field-container" style="position: relative;">
                            <button class="btn btn-outline-secondary time-field-btn" type="button" id="toTimeButton">
                                <span id="toTimeDisplay">now</span>
                                <i class="bi bi-chevron-down ms-2"></i>
                            </button>
                            <div class="time-dropdown-menu" id="toTimeMenu" style="display: none;">
                                <!-- Tabs -->
                                <div class="time-tabs">
                                    <button class="time-tab" data-tab="absolute" data-field="to">Absolute</button>
                                    <button class="time-tab active" data-tab="now" data-field="to">Now</button>
                                </div>

                                <!-- Tab Content -->
                                <div class="time-tab-content">
                                    <!-- Absolute Tab -->
                                    <div class="tab-pane" id="to-absolute">
                                        <div class="absolute-datetime-picker">
                                            <div class="row g-2 mb-3">
                                                <div class="col-6">
                                                    <label class="form-label small">Date</label>
                                                    <input type="date" class="form-control form-control-sm" id="toDatePicker">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label small">Time</label>
                                                    <input type="time" class="form-control form-control-sm" id="toTimePicker" step="1">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="end-date-display">
                                            <strong>End date:</strong> <span id="toDateDisplay">Jul 19, 2025 @ 14:45:40.033</span>
                                        </div>
                                        <div class="d-flex justify-content-end">
                                            <button class="btn btn-primary btn-sm" id="toSetAbsolute">Set</button>
                                        </div>
                                    </div>

                                    <!-- Now Tab -->
                                    <div class="tab-pane active" id="to-now">
                                        <div class="text-center p-3">
                                            <p>Set end time to now</p>
                                            <button class="btn btn-primary" id="toSetNow">Set to now</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>

                    <!-- Hidden inputs for form submission -->
                    <input type="hidden" id="timeRangeFrom" value="">
                    <input type="hidden" id="timeRangeTo" value="">
                    <input type="hidden" id="timeRange" value="15m">
                </div>

                <!-- Main Search Row -->
                <div class="mb-3">
                    <div class="d-flex gap-2 align-items-start">
                        <div class="search-input-container flex-grow-1">
                            <div class="input-group">
                                <input type="text" id="opensearchQuery" class="form-control" placeholder="Enter search query (message ID, simple text, or OpenSearch DSL JSON)..." autocomplete="off">
                                <button id="searchHistoryButton" class="btn btn-outline-secondary" type="button" title="Search History">
                                    <i class="bi bi-clock-history"></i>
                                </button>
                            </div>
                            <div id="searchHistoryDropdown" class="search-history-dropdown">
                                <div class="search-history-header">
                                    <span>Recent Searches</span>
                                    <button id="clearSearchHistory" class="btn btn-sm btn-outline-danger" title="Clear History">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                                <div id="searchHistoryList" class="search-history-list">
                                    <div class="search-history-empty">No recent searches</div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <div class="d-flex align-items-center">
                                <label for="searchResultCount" class="form-label me-2 mb-0 small">Results:</label>
                                <select id="searchResultCount" class="form-select form-select-sm" style="width: auto;">
                                    <option value="100">100</option>
                                    <option value="250">250</option>
                                    <option value="500" selected>500</option>
                                    <option value="1000">1000</option>
                                    <option value="2000">2000</option>
                                    <option value="3000">3000 (Max Recommended)</option>
                                </select>
                            </div>
                            <button id="opensearchSearchButton" class="btn btn-primary">
                                <i class="bi bi-search me-1"></i>Search
                            </button>
                            <button class="btn btn-outline-danger" id="opensearchClearButton">
                                <i class="bi bi-x-lg me-1"></i>Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Advanced Search Toggle -->
                <div class="mb-3">
                    <a href="#" id="advancedSearchToggle" class="text-decoration-none">
                        <i class="bi bi-chevron-down me-1"></i>Advanced Search
                    </a>
                </div>

                <!-- Advanced Search Dropdown (Initially Hidden) -->
                <div id="advancedSearchDropdown" class="mb-3" style="display: none;">
                    <div class="card">
                        <div class="card-body">
                            <!-- First Row: Message ID, Status Code, Client ID, App Name -->
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="advMessageId" class="form-label">Message ID</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control advanced-field" id="advMessageId" placeholder="e.g., apigeeprod112-24056-2385438-1">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="clearAdvancedField('advMessageId')" title="Clear">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="advStatusCode" class="form-label">Status Code</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control advanced-field" id="advStatusCode" placeholder="e.g., 500, 200">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="clearAdvancedField('advStatusCode')" title="Clear">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="advClientId" class="form-label">Client ID</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control advanced-field" id="advClientId" placeholder="e.g., mobile-app">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="clearAdvancedField('advClientId')" title="Clear">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="advAppName" class="form-label">App Name</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control advanced-field" id="advAppName" placeholder="e.g., my-api-app">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="clearAdvancedField('advAppName')" title="Clear">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Second Row: URI and Message Body -->
                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <label for="advUri" class="form-label">URI/Path</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control advanced-field" id="advUri" placeholder="e.g., /api/v1/users">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="clearAdvancedField('advUri')" title="Clear">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="advMessageBody" class="form-label">Message Body Contains</label>
                                    <div class="input-group">
                                        <textarea class="form-control advanced-field" id="advMessageBody" rows="2" placeholder="Search in message bodies..."></textarea>
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="clearAdvancedField('advMessageBody')" title="Clear">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container hidden" id="processedContainer">
        <div class="text-center">
            <h3>Processed logs</h3>

            <!-- Moved entry navigation elsewhere -->
        </div>

        <!-- Enhanced search interface -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="bi bi-search search-icon"></i>
                <input type="text"
                       id="logSearchInput"
                       class="search-input"
                       placeholder="Search logs...">

                <div class="search-controls">
                    <div class="search-options">
                        <div class="global-search-toggle-wrapper" title="Advanced search options">
                            <input type="checkbox" id="globalSearchToggle" class="global-search-toggle">
                            <label for="globalSearchToggle" class="global-search-label">Global</label>
                        </div>
                    </div>

                    <div class="search-navigation">
                        <button id="prevMatchBtn" class="search-nav-btn" title="Previous match" disabled>
                            <i class="bi bi-chevron-up"></i>
                        </button>
                        <span id="searchMatchCount" class="search-match-count" title="Total matches">0 matches</span>
                        <button id="nextMatchBtn" class="search-nav-btn" title="Next match" disabled>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>

                    <button id="clearSearchBtn" class="search-clear-btn" title="Clear search">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
            <div id="searchHelpText" class="search-help-text">
                <!-- Help text will be shown here when needed -->
            </div>
        </div>

        <!-- Column controls as dropdown -->
        <div class="log-entry-header" data-process-type="">
            <div class="dropdown view-type-selector">
                <button id="viewTypeButton" class="export-button" type="button">
                    <i class="bi bi-diagram-3 me-1"></i>
                    <span>API Calls</span>
                    <i class="bi bi-chevron-down ms-1"></i>
                </button>
                <div id="viewTypeControls" class="dropdown-menu">
                    <label class="dropdown-item" data-view-type="flows">
                        <i class="bi bi-list-ul me-2"></i>API Flows
                    </label>
                    <label class="dropdown-item" data-view-type="calls">
                        <i class="bi bi-diagram-3 me-2"></i>API Calls
                    </label>
                </div>
            </div>
            <div class="header-left">
                <span id="entryCounter" class="entry-counter">0 records found</span>
                <button id="copyButton" class="export-button">
                    <i class="bi bi-clipboard me-1"></i>
                    <span>Copy Text</span>
                </button>
                <button id="formatButton" class="export-button">
                    <i class="bi bi-code-slash me-1"></i>
                    <span>Format for OpenSearch</span>
                </button>
            </div>
            <div class="header-controls">
                <div class="dropdown">
                    <button id="flowButton" class="export-button" type="button">
                        <i class="bi bi-funnel me-1"></i>
                        <span>Flows</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="flowControls" class="dropdown-menu">
                        <label class="dropdown-item"><input type="checkbox" id="selectAllFlows" checked> All flows</label>
                        <div class="dropdown-divider"></div>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="PROXY_REQ_FLOW"> PROXY_REQ_FLOW</label>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="TARGET_REQ_FLOW"> TARGET_REQ_FLOW</label>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="TARGET_RESP_FLOW"> TARGET_RESP_FLOW</label>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="PROXY_RESP_FLOW"> PROXY_RESP_FLOW</label>
                    </div>
                </div>
                <div class="dropdown">
                    <button id="messageIdButton" class="export-button" type="button">
                        <i class="bi bi-funnel me-1"></i>
                        <span>Message IDs</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="messageIdControls" class="dropdown-menu" style="max-height: 300px; overflow-y: auto;">
                        <label class="dropdown-item"><input type="checkbox" id="selectAllMessageIds" checked> All message id</label>
                        <div class="dropdown-divider"></div>
                        <!-- Message IDs will be populated here -->
                    </div>
                </div>
                <div class="dropdown status-code-dropdown">
                    <button id="statusCodeButton" class="export-button" type="button">
                        <i class="bi bi-funnel me-1"></i>
                        <span>Status Codes</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="statusCodeControls" class="dropdown-menu" style="max-height: 300px; overflow-y: auto;">
                        <label class="dropdown-item"><input type="checkbox" id="selectAllStatusCodes" checked> All status codes</label>
                        <div class="dropdown-divider"></div>
                        <!-- Status codes will be populated here -->
                    </div>
                </div>
                <div class="dropdown">
                    <button id="columnButton" class="export-button" type="button">
                        <i class="bi bi-columns me-1"></i>
                        <span>Columns</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="columnControls" class="dropdown-menu">
                        <!-- Column checkboxes will be populated dynamically -->
                    </div>
                </div>
                <div class="dropdown">
                    <button id="exportButton" class="export-button" type="button">
                        <i class="bi bi-download me-1"></i>
                        <span>Export</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>                    <div id="exportControls" class="dropdown-menu">
                        <label class="dropdown-item" id="exportXLSX">
                            <i class="bi bi-file-earmark-spreadsheet me-2"></i><span class="export-label">Export <span class="export-type">flows</span> to XLSX</span>
                        </label>
                        <label class="dropdown-item" id="exportTXT">
                            <i class="bi bi-file-earmark-text me-2"></i>Export logs to TXT
                        </label>
                        <label class="dropdown-item" id="exportJPEG">
                            <i class="bi bi-file-earmark-image me-2"></i>Export current page to JPEG
                        </label>
                    </div>
                </div>
            </div>
        </div>
          <div id="fileContent" class="fileContent">
            <!-- Content will be inserted here by JavaScript -->
        </div>

        <button id="backToTopButton" class="back-to-top hidden" aria-label="Back to top">
            <i class="bi bi-arrow-up"></i>
        </button>
    </div>    <!-- Toast Notification -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1060">
        <div id="liveToast" class="toast border border-2 border-success" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <footer class="container text-center">
        <small>&copy; 2025 · Apigee Log Processor</small>
    </footer>    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- External libraries -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

    <!-- Modular JavaScript -->
    <script type="module">
        // Ensure external libraries are available to modules
        if (typeof XLSX !== 'undefined') {
            window.XLSX = XLSX;
        } else {
            console.error('XLSX library not loaded');
        }

        if (typeof html2canvas !== 'undefined') {
            window.html2canvas = html2canvas;
        } else {
            console.error('html2canvas library not loaded');
        }

        import { initializeApp } from './modules/main.js';
        // Initialize the app when the module is loaded
        document.addEventListener('DOMContentLoaded', async () => {
            await initializeApp();
        });
    </script>
    <!-- Offcanvas for entry details -->    <div class="offcanvas offcanvas-end responsive-offcanvas" tabindex="-1" id="entryDetailsOffcanvas" aria-labelledby="entryDetailsOffcanvasLabel">        <div class="offcanvas-header">
            <div class="offcanvas-header-content">
                <div class="offcanvas-header-top">
                    <div class="offcanvas-header-left">
                        <h5 class="offcanvas-title" id="entryDetailsOffcanvasLabel">
                            <span id="offcanvas-message-id">-</span>
                            <span class="separator">|</span>
                            <span id="offcanvas-flow">-</span>
                            <span class="separator">|</span>
                            <span id="offcanvas-env-org">-</span>
                            <span class="separator">|</span>
                            <span id="offcanvas-status-code">-</span>
                        </h5>
                    </div>
                    <div class="offcanvas-actions">
                        <button type="button" class="offcanvas-copy-btn" id="copyLogBtn" title="Copy">
                            <i class="bi bi-copy"></i>
                        </button>
                        <button type="button" class="offcanvas-copy-btn" id="exportLogBtn" title="Export to TXT">
                            <i class="bi bi-download"></i>
                        </button>
                        <button type="button" class="offcanvas-mask-btn" id="toggleMaskBtn" title="Show sensitive data">
                            <i class="bi bi-eye-slash"></i>
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                    </div>
                </div>
                <div class="offcanvas-header-bottom">
                    <div class="offcanvas-navigation">
                        <button type="button" class="nav-btn" id="prevLogBtn" title="Previous log (←)">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <span id="logNavigationCounter" class="log-nav-counter">Entry 0 of 0</span>
                        <button type="button" class="nav-btn" id="nextLogBtn" title="Next log (→)">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="offcanvas-body" id="offcanvasEntryContent">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3 text-center">
                <h5>Searching...</h5>
                <p class="text-muted">Please wait while we process your query</p>
            </div>
        </div>
    </div>



    <script type="module">
        // Set auto-connect data attribute immediately to hide config button
        import { OPENSEARCH_CONFIG } from './config/opensearch-config.js';
        if (OPENSEARCH_CONFIG.autoConnect.enabled && OPENSEARCH_CONFIG.ui.hideConfigurationButton) {
            document.body.setAttribute('data-auto-connect', 'true');
        }
    </script>
    <script type="module" src="modules/main.js"></script>
</body>
</html>

# OpenSearch API Gateway - Security Documentation

## Current Security Model

### 1. Authentication Flow

```
<PERSON>rows<PERSON> → API Gateway → OpenSearch Server
   ↓           ↓              ↓
No Creds   Stores Creds   Receives Auth
```

**Current Implementation:**
- <PERSON><PERSON><PERSON> sends credentials to API Gateway via `/configure` endpoint
- API Gateway stores credentials in memory
- API Gateway uses stored credentials for all OpenSearch requests
- <PERSON><PERSON><PERSON> never directly communicates with OpenSearch

### 2. Credential Storage (Current)

**Location:** Server memory (Node.js process)
**Lifetime:** Until server restart
**Scope:** Single API Gateway instance

```javascript
// Current storage in opensearch-gateway.js
let opensearchClient = null; // Stores configured client with credentials
```

**Security Characteristics:**
- ✅ Credentials not exposed to browser
- ✅ Credentials not stored in browser localStorage/cookies
- ✅ Credentials encrypted in transit (HTTPS)
- ❌ Credentials lost on server restart
- ❌ No session management
- ❌ No credential rotation
- ❌ No user isolation (single user per gateway instance)

### 3. Current Vulnerabilities

1. **No Session Management**
   - Once configured, gateway accepts all requests
   - No authentication required for subsequent API calls
   - No session expiration

2. **Memory-Only Storage**
   - Credentials lost on restart
   - No persistence across deployments

3. **Single User Model**
   - One set of credentials per gateway instance
   - No multi-user support

4. **No Audit Trail**
   - No logging of authentication events
   - No tracking of API usage

## Enhanced Security Architecture

### 1. Session-Based Authentication

```
Browser → Gateway (Session Auth) → OpenSearch
   ↓           ↓                      ↓
Session ID  Validates Session    Uses Stored Creds
```

### 2. Proposed Security Improvements

#### A. Session Management
- JWT-based session tokens
- Configurable session expiration
- Refresh token mechanism
- Secure session storage

#### B. Multi-User Support
- User isolation
- Per-user credential storage
- Role-based access control

#### C. Enhanced Security Features
- Password encryption at rest
- Audit logging
- Rate limiting
- CSRF protection

## Implementation Plan

### Phase 1: Basic Session Management

#### 1.1 Session Token System
```javascript
// Enhanced authentication flow
POST /auth/login
{
  "username": "user",
  "password": "pass",
  "opensearchEndpoint": "https://...",
  "opensearchUsername": "osuser",
  "opensearchPassword": "ospass"
}

Response:
{
  "success": true,
  "sessionToken": "jwt-token",
  "expiresIn": 3600,
  "refreshToken": "refresh-token"
}
```

#### 1.2 Protected API Endpoints
```javascript
// All API calls require session token
GET /api/opensearch/*
Headers: {
  "Authorization": "Bearer jwt-token"
}
```

#### 1.3 Session Validation Middleware
```javascript
function validateSession(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token || !isValidToken(token)) {
    return res.status(401).json({ error: 'Invalid session' });
  }
  req.user = decodeToken(token);
  next();
}
```

### Phase 2: Enhanced Security Features

#### 2.1 Encrypted Credential Storage
```javascript
const crypto = require('crypto');

class SecureCredentialStore {
  constructor(encryptionKey) {
    this.key = encryptionKey;
    this.sessions = new Map();
  }
  
  storeCredentials(sessionId, credentials) {
    const encrypted = this.encrypt(JSON.stringify(credentials));
    this.sessions.set(sessionId, {
      credentials: encrypted,
      createdAt: Date.now(),
      lastAccess: Date.now()
    });
  }
  
  encrypt(text) {
    const cipher = crypto.createCipher('aes-256-cbc', this.key);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }
}
```

#### 2.2 Session Cleanup and Expiration
```javascript
class SessionManager {
  constructor() {
    this.sessions = new Map();
    this.startCleanupTimer();
  }
  
  startCleanupTimer() {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60000); // Check every minute
  }
  
  cleanupExpiredSessions() {
    const now = Date.now();
    for (const [sessionId, session] of this.sessions) {
      if (now - session.lastAccess > SESSION_TIMEOUT) {
        this.sessions.delete(sessionId);
        console.log(`🗑️ Cleaned up expired session: ${sessionId}`);
      }
    }
  }
}
```

### Phase 3: Advanced Security Features

#### 3.1 Audit Logging
```javascript
class SecurityAuditLogger {
  log(event, sessionId, details = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      sessionId,
      ip: details.ip,
      userAgent: details.userAgent,
      success: details.success,
      error: details.error
    };
    
    // Log to file, database, or external service
    this.writeLog(logEntry);
  }
  
  logAuthAttempt(sessionId, success, ip, userAgent, error = null) {
    this.log('AUTH_ATTEMPT', sessionId, {
      ip, userAgent, success, error
    });
  }
  
  logApiAccess(sessionId, endpoint, method, ip) {
    this.log('API_ACCESS', sessionId, {
      endpoint, method, ip, success: true
    });
  }
}
```

#### 3.2 Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts',
  standardHeaders: true,
  legacyHeaders: false,
});

const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: 'Too many API requests',
});

app.use('/auth', authLimiter);
app.use('/api', apiLimiter);
```

## Enhanced Gateway Implementation

### 1. Secure Configuration Structure
```javascript
const config = {
  security: {
    sessionTimeout: 3600000, // 1 hour
    refreshTokenTimeout: 86400000, // 24 hours
    encryptionKey: process.env.ENCRYPTION_KEY,
    jwtSecret: process.env.JWT_SECRET,
    enableAuditLog: true,
    maxLoginAttempts: 5,
    lockoutDuration: 900000 // 15 minutes
  },
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
    credentials: true
  }
};
```

### 2. Enhanced Authentication Endpoints
```javascript
// Login endpoint
app.post('/auth/login', authLimiter, async (req, res) => {
  try {
    const { username, password, opensearchEndpoint, opensearchUsername, opensearchPassword } = req.body;
    
    // Validate input
    if (!username || !password || !opensearchEndpoint || !opensearchUsername || !opensearchPassword) {
      auditLogger.logAuthAttempt(null, false, req.ip, req.get('User-Agent'), 'Missing credentials');
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Test OpenSearch connection
    const testClient = new Client({
      node: opensearchEndpoint,
      auth: { username: opensearchUsername, password: opensearchPassword },
      ssl: { rejectUnauthorized: false }
    });
    
    await testClient.ping();
    
    // Create session
    const sessionId = crypto.randomUUID();
    const sessionToken = jwt.sign({ sessionId, username }, config.security.jwtSecret, {
      expiresIn: '1h'
    });
    const refreshToken = jwt.sign({ sessionId, type: 'refresh' }, config.security.jwtSecret, {
      expiresIn: '24h'
    });
    
    // Store encrypted credentials
    credentialStore.storeCredentials(sessionId, {
      opensearchEndpoint,
      opensearchUsername,
      opensearchPassword
    });
    
    auditLogger.logAuthAttempt(sessionId, true, req.ip, req.get('User-Agent'));
    
    res.json({
      success: true,
      sessionToken,
      refreshToken,
      expiresIn: 3600
    });
    
  } catch (error) {
    auditLogger.logAuthAttempt(null, false, req.ip, req.get('User-Agent'), error.message);
    res.status(401).json({ error: 'Authentication failed' });
  }
});

// Refresh token endpoint
app.post('/auth/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const decoded = jwt.verify(refreshToken, config.security.jwtSecret);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid refresh token');
    }
    
    const newSessionToken = jwt.sign(
      { sessionId: decoded.sessionId, username: decoded.username },
      config.security.jwtSecret,
      { expiresIn: '1h' }
    );
    
    res.json({
      success: true,
      sessionToken: newSessionToken,
      expiresIn: 3600
    });
    
  } catch (error) {
    res.status(401).json({ error: 'Invalid refresh token' });
  }
});

// Logout endpoint
app.post('/auth/logout', validateSession, (req, res) => {
  const sessionId = req.user.sessionId;
  credentialStore.removeSession(sessionId);
  auditLogger.log('LOGOUT', sessionId, { ip: req.ip });
  
  res.json({ success: true, message: 'Logged out successfully' });
});
```

### 3. Session Validation Middleware
```javascript
function validateSession(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }
    
    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, config.security.jwtSecret);
    
    // Check if session exists
    if (!credentialStore.hasSession(decoded.sessionId)) {
      return res.status(401).json({ error: 'Session expired or invalid' });
    }
    
    // Update last access time
    credentialStore.updateLastAccess(decoded.sessionId);
    
    req.user = decoded;
    next();
    
  } catch (error) {
    res.status(401).json({ error: 'Invalid session token' });
  }
}
```

## Client-Side Security Implementation

### 1. Secure Token Storage
```javascript
class SecureTokenManager {
  constructor() {
    this.sessionToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
  }
  
  storeTokens(sessionToken, refreshToken, expiresIn) {
    this.sessionToken = sessionToken;
    this.refreshToken = refreshToken;
    this.tokenExpiry = Date.now() + (expiresIn * 1000);
    
    // Store refresh token in secure httpOnly cookie if possible
    // For browser-only apps, use sessionStorage (not localStorage)
    sessionStorage.setItem('refreshToken', refreshToken);
  }
  
  async getValidToken() {
    if (!this.sessionToken) {
      throw new Error('No session token available');
    }
    
    // Check if token is about to expire (refresh 5 minutes before expiry)
    if (Date.now() > (this.tokenExpiry - 300000)) {
      await this.refreshSessionToken();
    }
    
    return this.sessionToken;
  }
  
  async refreshSessionToken() {
    const refreshToken = this.refreshToken || sessionStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }
    
    const response = await fetch('/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken })
    });
    
    if (!response.ok) {
      this.clearTokens();
      throw new Error('Token refresh failed');
    }
    
    const data = await response.json();
    this.storeTokens(data.sessionToken, refreshToken, data.expiresIn);
  }
  
  clearTokens() {
    this.sessionToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    sessionStorage.removeItem('refreshToken');
  }
}
```

### 2. Enhanced OpenSearch Client
```javascript
class SecureOpenSearchClient extends OpenSearchClient {
  constructor(config = {}) {
    super(config);
    this.tokenManager = new SecureTokenManager();
    this.isAuthenticated = false;
  }
  
  async authenticate(credentials) {
    try {
      const response = await fetch(`${this.config.gatewayUrl}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      });
      
      if (!response.ok) {
        throw new Error('Authentication failed');
      }
      
      const data = await response.json();
      this.tokenManager.storeTokens(data.sessionToken, data.refreshToken, data.expiresIn);
      this.isAuthenticated = true;
      
      console.log('✅ Authentication successful');
      return data;
      
    } catch (error) {
      console.error('❌ Authentication failed:', error);
      throw error;
    }
  }
  
  async makeRequest(path, options = {}) {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated. Please login first.');
    }
    
    try {
      const token = await this.tokenManager.getValidToken();
      
      const url = `${this.config.gatewayUrl}/api/opensearch${path}`;
      const requestOptions = {
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          ...options.headers
        },
        ...options
      };
      
      if (options.body && typeof options.body === 'object') {
        requestOptions.body = JSON.stringify(options.body);
      }
      
      const response = await fetch(url, requestOptions);
      
      if (response.status === 401) {
        // Token expired, try to refresh
        await this.tokenManager.refreshSessionToken();
        // Retry the request
        return this.makeRequest(path, options);
      }
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      if (error.message.includes('Token refresh failed')) {
        this.logout();
      }
      throw error;
    }
  }
  
  async logout() {
    try {
      if (this.isAuthenticated) {
        const token = await this.tokenManager.getValidToken();
        await fetch(`${this.config.gatewayUrl}/auth/logout`, {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` }
        });
      }
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      this.tokenManager.clearTokens();
      this.isAuthenticated = false;
      console.log('🔓 Logged out successfully');
    }
  }
}
```

## Security Best Practices

### 1. Environment Configuration
```bash
# .env file for production
ENCRYPTION_KEY=your-256-bit-encryption-key
JWT_SECRET=your-jwt-secret-key
SESSION_TIMEOUT=3600000
ALLOWED_ORIGINS=https://your-domain.com,https://another-domain.com
ENABLE_AUDIT_LOG=true
LOG_LEVEL=info
```

### 2. HTTPS Configuration
```javascript
// For production, always use HTTPS
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem')
};

https.createServer(options, app).listen(3001, () => {
  console.log('🔒 Secure API Gateway running on https://localhost:3001');
});
```

### 3. Security Headers
```javascript
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## Security Testing Guide

### 1. Authentication Testing
```bash
# Test login
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass",
    "opensearchEndpoint": "https://bslogmesprod1.hu:9200",
    "opensearchUsername": "osuser",
    "opensearchPassword": "ospass"
  }'

# Expected response:
{
  "success": true,
  "sessionToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "expiresIn": 3600,
  "user": { "username": "testuser", "sessionId": "uuid" }
}
```

### 2. Session Management Testing
```bash
# Test protected endpoint
TOKEN="your-session-token-here"
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/opensearch/

# Test token refresh
curl -X POST http://localhost:3001/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refreshToken": "your-refresh-token"}'

# Test logout
curl -X POST http://localhost:3001/auth/logout \
  -H "Authorization: Bearer $TOKEN"
```

### 3. Security Validation
```bash
# Test rate limiting (should fail after 5 attempts)
for i in {1..6}; do
  curl -X POST http://localhost:3001/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username":"wrong","password":"wrong"}'
done

# Test unauthorized access (should return 401)
curl http://localhost:3001/api/opensearch/

# Test invalid token (should return 401)
curl -H "Authorization: Bearer invalid-token" \
  http://localhost:3001/api/opensearch/
```

## Production Deployment Checklist

### 1. Environment Security
- [ ] Generate strong encryption keys
- [ ] Set secure JWT secrets
- [ ] Configure HTTPS certificates
- [ ] Set appropriate CORS origins
- [ ] Enable audit logging

### 2. Network Security
- [ ] Use HTTPS in production
- [ ] Configure firewall rules
- [ ] Set up reverse proxy (nginx/Apache)
- [ ] Implement IP whitelisting if needed

### 3. Monitoring and Logging
- [ ] Set up log rotation
- [ ] Monitor authentication failures
- [ ] Track API usage patterns
- [ ] Set up alerting for security events

### 4. Backup and Recovery
- [ ] Backup encryption keys securely
- [ ] Document recovery procedures
- [ ] Test session recovery
- [ ] Plan for key rotation

## Security Best Practices Summary

This enhanced security model provides:
- ✅ JWT-based session authentication
- ✅ Automatic token refresh
- ✅ AES-256-GCM encrypted credential storage
- ✅ Comprehensive audit logging
- ✅ Rate limiting protection
- ✅ Multi-user session isolation
- ✅ Secure logout with session cleanup
- ✅ CSRF protection via SameSite cookies
- ✅ Security headers (Helmet.js)
- ✅ Input validation and sanitization
- ✅ Configurable session timeouts
- ✅ Graceful error handling without information leakage

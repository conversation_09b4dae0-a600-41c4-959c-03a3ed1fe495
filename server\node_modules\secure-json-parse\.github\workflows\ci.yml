name: CI
on:
  push:
    paths-ignore:
      - docs/**
      - '*.md'
  pull_request:
    paths-ignore:
      - docs/**
      - '*.md'

jobs:
  dependency-review:
    name: Dependency Review
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Dependency review
        uses: actions/dependency-review-action@v3

  lint:
    name: Lint Code
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Install dependencies
        run: npm i --ignore-scripts

      - name: Lint code
        run: npm run lint

  browsers:
    name: Test Browsers
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Install dependencies
        run: npm i

      - name: Run tests
        run: npm run test:browser

  test:
    name: Test
    runs-on: ubuntu-latest
    permissions:
      contents: read
    strategy:
      matrix:
        node-version: [6, 8, 10, 11, 12, 13, 14, 15, 16, 18]
    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Setup Node ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Upgrade npm
        if: ${{ success() && matrix.node-version == '6' }}
        run: npm i npm@6.13.4 -g

      - name: Install dependencies
        run: npm i --ignore-scripts
        
      - name: Run tests
        run: npm run test:unit

  typescript:
    name: Test TypeScript
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Install dependencies
        run: npm i --ignore-scripts

      - name: tsd
        run: npm run test:typescript

  automerge:
    name: Automerge Dependabot PRs
    if: >
        github.event_name == 'pull_request' &&
        github.event.pull_request.user.login == 'dependabot[bot]'
    needs: [browsers, lint, test, typescript]
    permissions:
      pull-requests: write
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: fastify/github-action-merge-dependabot@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          target: major

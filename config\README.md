# OpenSearch Auto-Connection Configuration

This directory contains the configuration for automatic OpenSearch connection in the Apigee Log Processor.

## Setup Instructions

### 1. Configure Credentials

Edit the `opensearch-config.js` file and replace the placeholder values:

```javascript
export const OPENSEARCH_CONFIG = {
  autoConnect: {
    enabled: true,
    endpoint: 'https://bslogmesprod1.hu:9200',
    username: 'YOUR_INTERNAL_USERNAME', // ← Replace with actual username
    password: 'YOUR_INTERNAL_PASSWORD', // ← Replace with actual password
    indexPattern: 'apigee-prod-*',
    useApiServer: true,
    timeout: 30000
  },
  // ... rest of config
};
```

### 2. Security Considerations

- **Credentials Storage**: The credentials are stored in the JavaScript file, which means they are visible to anyone who has access to the source code.
- **Internal Use Only**: This approach is suitable for internal tools where the OpenSearch user has limited, read-only access.
- **Service Account**: Use a dedicated service account with minimal permissions for this connection.

### 3. Configuration Options

#### Auto-Connect Settings
- `enabled`: Set to `true` to enable auto-connection
- `endpoint`: OpenSearch cluster endpoint URL
- `username`: Internal service account username
- `password`: Internal service account password
- `indexPattern`: Default index pattern to search
- `useApiServer`: Always set to `true` (API server mode is required)
- `timeout`: Connection timeout in milliseconds

#### UI Settings
- `hideConfigurationButton`: Set to `true` to hide the manual configuration button
- `showConnectionStatus`: Set to `true` to show connection status indicator

### 4. How It Works

1. When the application starts, it checks if auto-connect is enabled
2. If enabled and no saved credentials exist, it validates the configuration
3. It automatically connects using the configured credentials
4. The configuration button is hidden from the UI
5. Connection status is displayed to the user

### 5. Troubleshooting

#### Configuration Errors
If you see "Configuration Error" messages:
- Check that username and password are not the default placeholder values
- Verify the endpoint URL is correct
- Ensure the service account has proper permissions

#### Connection Failures
If auto-connection fails:
- Verify the OpenSearch cluster is accessible
- Check that the service account credentials are valid
- Ensure the API server is running (if using API server mode)

### 6. Updating Credentials

When credentials need to be changed:
1. Edit the `opensearch-config.js` file
2. Update the username and/or password
3. Refresh the application - it will automatically use the new credentials

### 7. Disabling Auto-Connection

To disable auto-connection and return to manual configuration:
1. Set `enabled: false` in the configuration
2. Set `hideConfigurationButton: false` to show the config button again
3. Refresh the application

## File Structure

```
config/
├── opensearch-config.js    # Main configuration file
└── README.md              # This documentation
```

## Example Configuration

```javascript
export const OPENSEARCH_CONFIG = {
  autoConnect: {
    enabled: true,
    endpoint: 'https://bslogmesprod1.hu:9200',
    username: 'apigee-service-user',
    password: 'SecurePassword123!',
    indexPattern: 'apigee-prod-*',
    useApiServer: true,
    timeout: 30000
  },
  defaults: {
    indexPattern: 'apigee-prod-*',
    useApiServer: true
  },
  ui: {
    hideConfigurationButton: true,
    showConnectionStatus: true
  }
};
```

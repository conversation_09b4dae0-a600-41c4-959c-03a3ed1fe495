# OpenSearch Integration for Apigee Log Processor

## Overview

This integration allows the Apigee Log Processor to connect directly to OpenSearch clusters and search log data in real-time, bypassing the need for file uploads. **This is a browser-only solution that requires no server setup.**

## Quick Start

### 1. Open the Application

Simply open the application in your browser:
```
file:///path/to/your/project/apigeeLogProcessor.html
```

Or use any web server (Python, Live Server, etc.):
```bash
# Python 3
python -m http.server 8000

# Then open: http://localhost:8000/apigeeLogProcessor.html
```

### 2. Configure OpenSearch

1. Switch to "OpenSearch Mode" using the toggle switch
2. Select your desired index pattern from the dropdown:
   - `okd-test*`
   - `okd-prod*`
   - `apigee-e2e*`
   - `apigee-sit*`
   - `apigee-uat-pet-*`
   - `apigee-prod-*`
3. Click "Configure" button
4. Enter your OpenSearch credentials
5. Click "Test Connection"
6. **If connection fails**, follow the step-by-step guidance provided
7. If successful, click "Save Configuration"

### 3. Search OpenSearch

You can use either simple queries or full OpenSearch DSL:

**Simple Queries:**
- `error` - Search for "error" in all fields
- `messageId:12345` - Search for specific message ID
- `statusCode:500` - Search for 500 status codes
- `uri:/api/users` - Search for specific URI

**OpenSearch DSL (JSON):**
```json
{"match": {"messageBody": "error"}}
```

```json
{"term": {"statusCode": "500"}}
```

```json
{
  "bool": {
    "must": [
      {"match": {"messageBody": "error"}},
      {"term": {"statusCode": "500"}}
    ]
  }
}
```

## Architecture

### Browser-Only Approach

This solution works entirely in the browser with no server requirements:
- Direct connection from browser to OpenSearch
- Built-in guidance system for resolving CORS and certificate issues
- No proxy servers, Node.js, or external dependencies required
- Works with any web server or even file:// protocol

### Connection Issues & Solutions

The application includes an intelligent guidance system that helps resolve common issues:

1. **CORS Issues**: Step-by-step guidance for configuring OpenSearch CORS or using alternative methods
2. **Certificate Issues**: Automatic detection and guidance for accepting SSL certificates
3. **Connection Issues**: Troubleshooting steps for network and authentication problems

### Data Flow

```
Browser → OpenSearch (bslogmesprod1.hu:9200)
```

If direct connection fails, the guidance system provides alternative approaches.

## Configuration

### OpenSearch Client Configuration

The client is pre-configured for your environment:
- **Endpoint**: `https://bslogmesprod1.hu:9200`
- **Authentication**: Basic auth (username/password)
- **Certificate**: Loaded from `cert/bslogmesprod1.vodafone.hu.pem`

### Index Patterns

Available index patterns:
- `okd-test*` - OKD test environment logs
- `okd-prod*` - OKD production environment logs
- `apigee-e2e*` - Apigee end-to-end test logs
- `apigee-sit*` - Apigee system integration test logs
- `apigee-uat-pet-*` - Apigee UAT performance test logs
- `apigee-prod-*` - Apigee production logs (default)

## Features

### Dual Mode Operation
- **File Mode**: Traditional file upload and processing
- **OpenSearch Mode**: Real-time search against OpenSearch clusters

### Query Support
- Simple text queries with field-specific syntax
- Full OpenSearch Query DSL support
- Real-time search with pagination
- Compatible with existing UI (tables, filters, offcanvas)

### Security
- Encrypted credential storage
- Session-based or persistent credential storage
- Certificate-based authentication support

## Troubleshooting

### Common Issues

**1. Proxy Server Not Starting**
- Ensure Node.js is installed
- Run `npm install` to install dependencies
- Check if port 3001 is available

**2. Connection Errors**
- Verify your OpenSearch credentials
- Check if the OpenSearch server is accessible
- Ensure the certificate file is in the correct location

**3. CORS Errors**
- Make sure you're accessing the app through the proxy: `http://localhost:3001/apigeeLogProcessor.html`
- Don't access the files directly (file:// protocol)

**4. Certificate Errors**
- Ensure `cert/bslogmesprod1.vodafone.hu.pem` exists
- The proxy will show certificate status on startup

### Debug Mode

Open browser console and use these debug functions:
```javascript
// Test OpenSearch connection
testOpenSearchConnection()

// Access OpenSearch client directly
opensearchClient.testConnection()

// Access credential manager
credentialManager.getCredentials()
```

## Files Structure

```
├── opensearch-proxy.js          # Proxy server
├── package.json                 # Node.js dependencies
├── start-proxy.bat             # Windows startup script
├── cert/
│   └── bslogmesprod1.vodafone.hu.pem  # SSL certificate
├── modules/
│   ├── opensearch-client.js     # OpenSearch client
│   ├── credential-manager.js    # Credential management
│   ├── opensearch-transformer.js # Data transformation
│   ├── mode-manager.js          # Mode switching
│   └── ...
└── apigeeLogProcessor.html      # Main application
```

## Support

For issues or questions:
1. Check the browser console for error messages
2. Verify proxy server logs
3. Test OpenSearch connectivity using curl:
   ```bash
   curl -k -u "username:password" "https://bslogmesprod1.hu:9200/_cluster/health"
   ```

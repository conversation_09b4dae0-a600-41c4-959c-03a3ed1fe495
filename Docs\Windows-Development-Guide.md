# OpenSearch API Server - Windows Development Guide

## Quick Start

### Prerequisites
- Node.js 14+ (already installed)
- Access to your OpenSearch server

### Setup and Run
```batch
# Option 1: Automated setup
setup-server.bat

# Option 2: Manual setup
cd server
npm install
npm start
```

### Access
- **Application**: http://127.0.0.1:3001
- **Health Check**: http://127.0.0.1:3001/health

## What This Solves

### The CORS Problem
- OpenSearch JavaScript client is **NOT designed for browsers**
- Direct browser connections cause CORS errors
- Even `--disable-web-security` doesn't work reliably

### Our Solution
- **API Server** runs the official OpenSearch client on Node.js server
- **Browser** talks to local server (no CORS issues)
- **Credentials** stay secure on server-side
- **Zero configuration** needed on OpenSearch server

## How to Test OpenSearch Integration

### 1. Start the Server
```batch
setup-server.bat
```

### 2. Configure in Browser
1. Open http://127.0.0.1:3001
2. Switch to "OpenSearch Mode"
3. Click "Configure" button
4. **Enable "Use API Server"** checkbox
5. Enter your credentials:
   - **Endpoint**: `https://bslogmesprod1.hu:9200`
   - **Username**: [your OpenSearch username]
   - **Password**: [your OpenSearch password]
6. Click "Test Connection"

### 3. Verify Success
- Should see "✅ Connection successful!"
- Check browser console for detailed logs
- No CORS errors should appear

## Architecture

```
Browser (Your App) → API Server (Node.js) → OpenSearch Server
       ↓                    ↓                      ↓
   No CORS Issues    Official Client        Receives Auth
   No Credentials    Handles Security       Returns Data
```

## Development Workflow

### Starting Development
```batch
# Start the server
setup-server.bat

# Or for testing
test-gateway-windows.bat
```

### Making Changes
1. Edit your frontend code normally
2. Server serves static files automatically
3. Refresh browser to see changes
4. Server restart not needed for frontend changes

### Debugging
- **Browser Console**: Frontend debugging
- **Server Console**: Server-side logs
- **Health Check**: http://127.0.0.1:3001/health

## Configuration

### Client Configuration
In your JavaScript code:
```javascript
// Enable server mode
opensearchClient.configureServer(true, 'http://127.0.0.1:3001');

// Test connection
const result = await opensearchClient.testConnection();
```

### Server Configuration
The server automatically:
- Serves your application files
- Handles CORS headers
- Routes API requests to OpenSearch
- Manages credentials securely

## Troubleshooting

### Common Issues

**1. "Failed to install dependencies"**
- Check internet connection
- Try: `cd server && npm install --verbose`

**2. "Connection failed"**
- Verify OpenSearch server is accessible
- Check credentials are correct
- Ensure "Use API Gateway" is enabled

**3. "Page not loading"**
- Check if port 3001 is available
- Try different port: `set PORT=3002 && npm start`

**4. CORS errors still appearing**
- Make sure "Use API Server" is checked
- Verify server URL is `http://127.0.0.1:3001`
- Check browser console for configuration errors

### Debug Commands
```batch
# Check if server is running
curl http://127.0.0.1:3001/health

# View detailed logs
cd server
npm start
```

## File Structure (Development)

```
├── server/
│   ├── opensearch-gateway.js     # Main API server
│   ├── package.json              # Dependencies
│   └── node_modules/             # Installed packages
├── modules/
│   └── opensearch-client.js      # Client with server support
├── index.html                    # Main application (default)
├── apigeeLogProcessor.html       # Apigee Log Processor
├── setup-server.bat              # Windows setup script
├── test-gateway-windows.bat      # Testing script
└── Docs/
    └── Windows-Development-Guide.md  # This guide
```

## Next Steps

### For Development
1. ✅ Get basic gateway working (this guide)
2. 🔄 Test OpenSearch integration
3. 🔄 Develop your features
4. 🔄 Prepare for production deployment

### For Production
- See `Docs/README-Linux-Deployment.md` for production setup
- Consider security enhancements in `Docs/SECURITY-DOCUMENTATION.md`

## Support

If you encounter issues:
1. Check this troubleshooting section
2. Review browser console for errors
3. Check gateway console output
4. Verify OpenSearch server connectivity

This setup provides a clean development environment that solves CORS issues while keeping your workflow simple and focused on Windows development.

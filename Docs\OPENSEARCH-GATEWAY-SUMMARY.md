# OpenSearch API Gateway - Complete Solution

## What We Built

Instead of calling it a "proxy," we created an **OpenSearch API Gateway** that uses the **official OpenSearch JavaScript client** to provide secure browser access to your OpenSearch cluster.

## Architecture

```
Browser (Your App) → API Gateway (Node.js + Official Client) → OpenSearch Server
```

## Key Components

### 1. **OpenSearch API Gateway** (`server/opensearch-gateway.js`)
- Uses the official `@opensearch-project/opensearch` client
- Handles authentication securely on the server-side
- Provides REST API endpoints for browser access
- Eliminates CORS issues completely

### 2. **Updated Client** (`modules/opensearch-client.js`)
- Added `configureGateway()` method
- Routes requests through `/api/opensearch/*` endpoints
- Maintains existing functionality for direct connections

### 3. **Linux/RHEL Deployment Scripts**
- `setup-gateway.sh` - Quick development setup
- `install-service.sh` - Production service installation
- PM2 process manager integration

## For Your RHEL Machine

### Quick Start
```bash
# Make scripts executable (on Linux)
chmod +x setup-gateway.sh install-service.sh

# Development mode
./setup-gateway.sh

# Production service
sudo ./install-service.sh
```

### Manual Setup
```bash
cd server
npm install
npm start
```

### Access
- Application: `http://localhost:3001`
- API: `http://localhost:3001/api/opensearch/*`
- Health: `http://localhost:3001/health`

## How It Solves CORS

### The Problem
- OpenSearch JavaScript client is **NOT designed for browsers**
- Direct browser connections cause CORS errors
- Even `--disable-web-security` doesn't reliably work

### Our Solution
- **Server-side official client** handles OpenSearch communication
- **Browser makes requests to local API Gateway** (same origin)
- **No CORS issues** because browser talks to same-origin server
- **Secure credential handling** - credentials never exposed to browser

## Configuration in Your App

```javascript
// Enable API Gateway mode
opensearchClient.configureGateway(true, 'http://localhost:3001');

// Test connection
const result = await opensearchClient.testConnection();
```

## Production Features

### PM2 Process Manager
```bash
# Start as service
pm2 start opensearch-gateway.js --name opensearch-gateway

# Auto-restart on boot
pm2 startup
pm2 save

# Monitor
pm2 monit
```

### Service Management
```bash
pm2 start opensearch-gateway    # Start
pm2 stop opensearch-gateway     # Stop
pm2 restart opensearch-gateway  # Restart
pm2 logs opensearch-gateway     # View logs
```

## Security Benefits

1. **Official Client** - Uses OpenSearch's recommended approach
2. **Server-side Credentials** - No exposure to browser
3. **HTTPS Ready** - Can be deployed behind reverse proxy
4. **Session Management** - Secure authentication handling

## File Structure

```
├── server/
│   ├── opensearch-gateway.js     # Main API Gateway server
│   ├── package.json              # Dependencies and scripts
│   └── ecosystem.config.js       # PM2 configuration (auto-generated)
├── modules/
│   └── opensearch-client.js      # Updated client with gateway support
├── setup-gateway.sh              # Linux development setup
├── install-service.sh            # Linux production installation
├── setup-proxy.bat               # Windows setup (legacy name)
├── README-Linux-Deployment.md    # Complete Linux deployment guide
└── README-CORS-Solutions.md      # All CORS solutions explained
```

## Why This Approach

### ✅ Advantages
- Uses **official OpenSearch JavaScript client**
- **No CORS issues** - completely eliminated
- **Production ready** with PM2
- **Secure** - credentials on server-side only
- **Scalable** - can run multiple instances
- **Maintainable** - follows OpenSearch best practices

### ❌ Alternative Approaches Issues
- **Direct browser connection**: Not recommended by OpenSearch
- **CORS configuration**: Requires admin access, security risks
- **Browser flags**: Development only, unreliable

## Next Steps

1. **Deploy on your RHEL machine** using the provided scripts
2. **Test the connection** to your OpenSearch cluster
3. **Configure your app** to use gateway mode
4. **Set up production monitoring** with PM2

This solution provides the most robust, secure, and maintainable approach to accessing OpenSearch from browser applications while following official recommendations.

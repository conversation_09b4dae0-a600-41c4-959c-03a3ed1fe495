# OpenSearch API Gateway - Linux/RHEL Deployment

## Overview

This solution uses the **official OpenSearch JavaScript client** in a Node.js API Gateway to provide secure browser access to OpenSearch without CORS issues.

## Architecture

```
Browser → API Gateway (Node.js + Official Client) → OpenSearch Server
```

**Benefits:**
- ✅ Uses official OpenSearch JavaScript client
- ✅ No CORS issues
- ✅ Secure credential handling
- ✅ Production-ready with PM2
- ✅ RHEL/Linux compatible

## Prerequisites

### RHEL/CentOS/Fedora
```bash
# Install Node.js and npm
sudo dnf install nodejs npm

# Verify installation
node --version  # Should be 14.0.0 or higher
npm --version
```

### Ubuntu/Debian
```bash
# Install Node.js and npm
sudo apt update
sudo apt install nodejs npm

# Verify installation
node --version  # Should be 14.0.0 or higher
npm --version
```

## Quick Start

### Option 1: Development Mode
```bash
# Make script executable
chmod +x setup-gateway.sh

# Run the setup script
./setup-gateway.sh
```

### Option 2: Production Service
```bash
# Make script executable
chmod +x install-service.sh

# Install as system service (requires sudo)
sudo ./install-service.sh
```

## Manual Setup

### 1. Install Dependencies
```bash
cd server
npm install
```

### 2. Start the Gateway
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

### 3. Access the Application
Open your browser and go to: `http://localhost:3001`

## Production Deployment

### Using PM2 Process Manager

```bash
# Install PM2 globally
sudo npm install -g pm2

# Start the service
cd server
pm2 start opensearch-gateway.js --name opensearch-gateway

# Save PM2 configuration
pm2 save

# Setup auto-start on boot
pm2 startup
```

### Service Management Commands
```bash
# Start service
pm2 start opensearch-gateway

# Stop service
pm2 stop opensearch-gateway

# Restart service
pm2 restart opensearch-gateway

# View status
pm2 status

# View logs
pm2 logs opensearch-gateway

# Monitor in real-time
pm2 monit
```

## Configuration

### 1. Configure OpenSearch Connection

In your browser application:
1. Switch to "OpenSearch Mode"
2. Click "Configure"
3. Enable "Use API Gateway"
4. Enter your OpenSearch credentials
5. Test connection

### 2. Environment Variables (Optional)

Create a `.env` file in the `server` directory:
```bash
PORT=3001
NODE_ENV=production
OPENSEARCH_ENDPOINT=https://bslogmesprod1.hu:9200
```

## Firewall Configuration

### RHEL/CentOS/Fedora (firewalld)
```bash
# Open port 3001
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload
```

### Ubuntu/Debian (ufw)
```bash
# Open port 3001
sudo ufw allow 3001/tcp
```

## Security Considerations

### 1. Network Security
- Run behind a reverse proxy (nginx/Apache) in production
- Use HTTPS with SSL certificates
- Restrict access to specific IP ranges

### 2. Authentication
- OpenSearch credentials are stored server-side only
- No credentials exposed to browser
- Session-based authentication

### 3. Example Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Find process using port 3001
sudo lsof -i :3001

# Kill the process
sudo kill -9 <PID>
```

**2. Permission Denied**
```bash
# Make scripts executable
chmod +x *.sh

# Check file permissions
ls -la *.sh
```

**3. Node.js Version Issues**
```bash
# Check Node.js version
node --version

# Update Node.js (using NodeSource repository)
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install nodejs
```

**4. Service Logs**
```bash
# View PM2 logs
pm2 logs opensearch-gateway

# View system logs
journalctl -u opensearch-gateway

# View application logs
tail -f /var/log/opensearch-gateway.log
```

## API Endpoints

The API Gateway exposes these endpoints:

- `GET /` - Serve the main application
- `POST /configure` - Configure OpenSearch connection
- `GET /health` - Health check
- `ALL /api/opensearch/*` - OpenSearch API proxy

## Performance Tuning

### 1. PM2 Cluster Mode
```bash
# Start multiple instances
pm2 start opensearch-gateway.js -i max --name opensearch-gateway
```

### 2. Memory Limits
```bash
# Set memory limit
pm2 start opensearch-gateway.js --max-memory-restart 1G
```

### 3. Log Rotation
```bash
# Install PM2 log rotate
pm2 install pm2-logrotate

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## Monitoring

### 1. PM2 Monitoring
```bash
# Real-time monitoring
pm2 monit

# Web-based monitoring (optional)
pm2 plus
```

### 2. System Resources
```bash
# Check system resources
htop
free -h
df -h
```

## Backup and Recovery

### 1. Backup Configuration
```bash
# Backup PM2 configuration
pm2 save

# Backup application files
tar -czf opensearch-gateway-backup.tar.gz server/
```

### 2. Recovery
```bash
# Restore PM2 configuration
pm2 resurrect

# Restore application files
tar -xzf opensearch-gateway-backup.tar.gz
```

This setup provides a robust, production-ready solution for accessing OpenSearch from your browser applications on RHEL/Linux systems.

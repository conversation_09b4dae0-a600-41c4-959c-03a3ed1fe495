# MWWebTool - OpenSearch Integration

## Quick Start (Windows Development)

### 1. Setup OpenSearch API Server
```batch
setup-server.bat
```

### 2. Access Application
Open your browser to: **http://127.0.0.1:3001**

### 3. Configure OpenSearch
1. Switch to "OpenSearch Mode"
2. Click "Configure" and enable "Use API Server"
3. Enter your OpenSearch credentials
4. Test connection

## What This Solves

**Problem**: OpenSearch JavaScript client causes CORS errors in browsers

**Solution**: API Server runs the official OpenSearch client on Node.js server, eliminating CORS issues while keeping credentials secure.

## Architecture

```
Browser → API Server (Node.js) → OpenSearch Server
   ↓           ↓                      ↓
No CORS    Official Client      Secure Connection
```

## Documentation

- **[Windows Development Guide](Docs/Windows-Development-Guide.md)** - Complete setup and troubleshooting
- **[OpenSearch Integration](Docs/OpenSearch_Integration_Architecture.md)** - Technical architecture
- **[CORS Solutions](Docs/README-CORS-Solutions.md)** - All available solutions explained

## File Structure

```
├── server/                    # API Server
│   ├── opensearch-gateway.js  # Main server
│   └── package.json          # Dependencies
├── modules/                   # Frontend modules
│   └── opensearch-client.js   # Client with server support
├── index.html                 # Main application (default)
├── apigeeLogProcessor.html    # Apigee Log Processor
├── setup-server.bat           # Windows setup script
└── Docs/                     # Documentation
```

## Requirements

- Node.js 14+ (already installed)
- Access to OpenSearch server

## Support

For issues, check:
1. [Windows Development Guide](Docs/Windows-Development-Guide.md) troubleshooting section
2. Browser console for errors
3. Gateway console output

This setup provides a clean, Windows-focused development environment that solves OpenSearch CORS issues using the official JavaScript client.

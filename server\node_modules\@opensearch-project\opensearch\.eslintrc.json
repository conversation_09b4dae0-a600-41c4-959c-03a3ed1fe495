{
    "env": {
        "node": true,
        "es6": true
    },
    "extends": [
        "eslint:recommended",
        "prettier"
    ],
    "parser": "@babel/eslint-parser",
    "parserOptions": {
        "requireConfigFile": false
    },
    "plugins": [
        "prettier"
    ],
    "rules": {
        "prettier/prettier": [
            "error",
            {
                "endOfLine": "auto"
            }
        ],
        /*temporarily turn off no-dupe-else-if, open an issue to track https://github.com/opensearch-project/opensearch-js/issues/240*/
        "no-dupe-else-if": "off",
        "no-unused-vars": [
            "error",
            {
                "varsIgnorePattern": "^_.*",
                "argsIgnorePattern": "^_.*"
            }
        ]
    }
}

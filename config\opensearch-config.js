/**
 * OpenSearch Configuration
 * 
 * This file contains the configuration for automatic OpenSearch connection.
 * Update these values when credentials or endpoints change.
 */

export const OPENSEARCH_CONFIG = {
  // Auto-connection settings
  autoConnect: {
    enabled: true,
    endpoint: 'https://bslogmesprod1.vodafone.hu:9200',
    username: 'wsrvMWToolbox', // Replace with actual internal username
    password: '.+958Xo)}VY', // Replace with actual internal password
    indexPattern: 'apigee-prod-*',
    useApiServer: true, // Always use API server - no other options needed
    timeout: 120000 // Connection and search timeout in milliseconds (2 minutes)
  },

  // Default settings
  defaults: {
    indexPattern: 'apigee-prod-*',
    useApiServer: true
  },

  // UI settings
  ui: {
    hideConfigurationButton: true, // Hide config button when auto-connect is enabled
    showConnectionStatus: true     // Show connection status indicator
  }
};

/**
 * Validate configuration
 */
export function validateConfig() {
  const config = OPENSEARCH_CONFIG.autoConnect;
  
  if (!config.endpoint) {
    throw new Error('OpenSearch endpoint is required');
  }
  
  if (!config.username || config.username === 'YOUR_INTERNAL_USERNAME') {
    throw new Error('OpenSearch username must be configured');
  }
  
  if (!config.password || config.password === 'YOUR_INTERNAL_PASSWORD') {
    throw new Error('OpenSearch password must be configured');
  }
  
  if (!config.indexPattern) {
    throw new Error('OpenSearch index pattern is required');
  }
  
  return true;
}

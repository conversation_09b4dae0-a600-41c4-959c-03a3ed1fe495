/**
 * Log Processor for Apigee Log Processor
 * Handles parsing and processing of log data
 */
import { stateManager } from './state.js';
import { maskSensitiveData, maskContent } from './utilities.js';
import { opensearchClient } from './opensearch-client.js';
import { opensearchTransformer } from './opensearch-transformer.js';
import { opensearchQueryBuilder } from './opensearch-query-builder.js';
import { uiManager } from './ui.js';

export class LogProcessor {
  constructor() {
    // Status code styling configuration
    this.statusCodeStyles = {
      '1': 'background-color: #81c3c9; color: #000; font-size: 1.1em; padding: 0.4em 0.8em; border-radius: 0.75em;',
      '2': 'background-color: #6ac66b; color: #fff; font-size: 1.1em; padding: 0.4em 0.8em; border-radius: 0.75em;',
      '3': 'background-color: #f8d65d; color: #000; font-size: 1.1em; padding: 0.4em 0.8em; border-radius: 0.75em;',
      '4': 'background-color: #ffbd59; color: #000; font-size: 1.1em; padding: 0.4em 0.8em; border-radius: 0.75em;',
      '5': 'background-color: #ff5757; color: #fff; font-size: 1.1em; padding: 0.4em 0.8em; border-radius: 0.75em;'
    };
  }

  /**
   * Process log content from text or file
   * @param {string} action - The action to perform ('flow', 'mask', or 'extract')
   * @param {string|File} input - The input text or file
   */
  processData(action, input) {
    // Clear search input and related state when processing new data
    this.clearSearchState();

    if (input instanceof File) {
      this.processFile(action, input);
    } else if (typeof input === 'string' && input.trim()) {
      this.processText(action, input.trim());
    } else {
      // Use a simple alert instead of uiManager to avoid circular dependency
      alert('Please provide input data first');
    }
  }

  /**
   * Process OpenSearch query
   * @param {string} query - The OpenSearch query string
   * @param {Object} options - Search options
   */
  async processOpenSearchQuery(query, options = {}) {
    console.log('Processing OpenSearch query:', query);

    // Clear search state
    this.clearSearchState();

    try {
      // Store the desired number of flows for later use
      this.maxFlowsFromOptions = options.size || 500;

      // Check for very large result sets and warn user
      if (this.maxFlowsFromOptions > 3000) {
        this.showErrorNotification(`Warning: Requesting ${this.maxFlowsFromOptions} results may cause performance issues. Consider using a smaller result set or narrower time range.`);
      }

      // Convert simple query to OpenSearch Query DSL
      const timeRange = options.timeRange || '1d';
      const searchQuery = this.buildOpenSearchQuery(query, timeRange);

      // Execute search - use a larger size to ensure we get enough entries to find the desired number of unique flows
      // We multiply by 3 as a heuristic since some flows may have multiple entries
      // Cap the initial search size to prevent OpenSearch from hitting limits
      const initialSearchSize = Math.min((options.size || 500) * 3, 10000); // Cap at 10k entries
      const searchOptions = {
        size: initialSearchSize,
        from: options.from || 0,
        sort: options.sort || [{ '@timestamp': { order: 'asc' } }] // Ascending order - earliest first
      };

      const searchResult = await opensearchClient.search(searchQuery, searchOptions);

      if (searchResult.success) {
        // Transform OpenSearch response to app data structure
        const transformed = opensearchTransformer.transformSearchResponse(searchResult.rawResponse);

        if (transformed.success) {
          // Check if we have any results
          if (transformed.entries.length === 0) {
            console.log('No results found for query:', query);

            // Clear previous state and update UI properly
            stateManager.setState({
              opensearchResults: [],
              opensearchQuery: query,
              opensearchTotalHits: 0,
              entries: []
            });

            // Clear UI and show no results message
            this.clearUIForNoResults();
            this.showNoResultsMessage(query);

            return {
              success: true,
              message: 'No results found',
              entries: [],
              totalHits: 0
            };
          }

          // Update state with OpenSearch results
          stateManager.setState({
            opensearchResults: transformed.entries,
            opensearchQuery: query,
            opensearchTotalHits: transformed.totalHits,
            entries: transformed.entries // Use OpenSearch results as main entries
          });

          // Initialize filter states
          this.initializeFilterStates(transformed.entries);

          // Enhance entries with complete flows for API call view
          const enhancedEntries = await this.enhanceWithCompleteFlows(transformed.entries, timeRange);

          // Update the entries with enhanced data for both views
          stateManager.setState({ entries: enhancedEntries });

          // Create API calls view from enhanced entries
          const calls = opensearchTransformer.createApiCallsFromEntries(enhancedEntries);
          stateManager.setState({ calls });

          // Set default view type to calls
          stateManager.updateState('currentViewType', 'calls');

          // Update body data attribute for CSS styling
          document.body.setAttribute('data-view-type', 'calls');

          // Show processed container and set process type
          const processedContainer = document.getElementById('processedContainer');
          if (processedContainer) {
            processedContainer.classList.remove('hidden');
          }

          // Set process type for CSS styling
          const header = document.querySelector('.log-entry-header');
          if (header) {
            header.dataset.processType = 'flow';
          }

          // Show the calls view
          uiManager.showCallSequenceView();

          // Dispatch event to update UI
          const event = new CustomEvent('entriesProcessed', {
            detail: {
              viewType: 'calls',
              source: 'opensearch',
              totalHits: transformed.totalHits
            }
          });
          document.dispatchEvent(event);

          return {
            success: true,
            entries: transformed.entries,
            totalHits: transformed.totalHits,
            took: transformed.took
          };
        } else {
          throw new Error('Failed to transform OpenSearch response');
        }
      } else {
        throw new Error(searchResult.error || 'OpenSearch query failed');
      }
    } catch (error) {
      console.error('OpenSearch processing failed:', error);

      // Show error to user using app notification
      this.showErrorNotification(`OpenSearch query failed: ${error.message}`);

      return {
        success: false,
        error: error.message,
        entries: [],
        totalHits: 0
      };
    }
  }



  /**
   * Build OpenSearch Query DSL from query string
   * @param {string} queryString - Query string (can be simple text or JSON DSL)
   * @param {string} timeRange - Time range (e.g., '1h', '1d', '7d')
   * @returns {Object} OpenSearch Query DSL
   */
  buildOpenSearchQuery(queryString, timeRange = '1d') {
    if (!queryString || !queryString.trim()) {
      // Default query with tracelog filter
      return {
        bool: {
          must: { match_all: {} },
          filter: {
            term: { "logtype.keyword": "tracelog" }
          }
        }
      };
    }

    const query = queryString.trim();

    // Check if the query is already JSON (OpenSearch DSL)
    if (query.startsWith('{')) {
      try {
        const parsedQuery = JSON.parse(query);
        console.log('Using provided OpenSearch DSL query');
        return parsedQuery;
      } catch (error) {
        console.warn('Invalid JSON query, falling back to simple query processing');
      }
    }

    // Simple query processing for non-JSON queries
    // Check for advanced search queries (contain field names with dots)
    if (query.includes('trace.') || query.includes('message:') || query.includes('event.original:')) {
      console.log('Detected advanced search query - using structured query');

      // Build filters array
      const filters = [
        { term: { "logtype.keyword": "tracelog" } }
      ];

      // Add time range filter
      if (timeRange && timeRange !== 'all') {
        // Check if timeRange contains ISO timestamps (from new time range picker)
        if (timeRange.includes('|') && timeRange.includes('T')) {
          const [fromTime, toTime] = timeRange.split('|');
          console.log(`Advanced search: Using absolute time range filter: ${fromTime} to ${toTime}`);
          filters.push({
            range: {
              "@timestamp": {
                gte: fromTime,
                lte: toTime
              }
            }
          });
        } else {
          // Legacy relative time format (15m, 1h, etc.)
          console.log(`Advanced search: Using relative time range filter: now-${timeRange}`);
          filters.push({
            range: {
              "@timestamp": {
                gte: `now-${timeRange}`
              }
            }
          });
        }
      }

      // URI wildcard queries are now handled directly with trace.uri field
      // No special processing needed as advanced search now uses correct field names

      // Extract operator from query if it contains explicit AND/OR
      let defaultOperator = "AND";
      if (query.includes(' OR ')) {
        defaultOperator = "OR";
      }

      return {
        bool: {
          must: {
            query_string: {
              query: query,
              default_operator: defaultOperator
            }
          },
          filter: filters
        }
      };
    }

    // Parse the query for AND/OR operators and quoted terms
    const baseQuery = this.parseSearchQuery(query);

    // Always add tracelog filter and time range
    const filters = [
      { term: { "logtype.keyword": "tracelog" } }
    ];

    // Add time range filter
    if (timeRange && timeRange !== 'all') {
      // Check if timeRange contains ISO timestamps (from new time range picker)
      if (timeRange.includes('|') && timeRange.includes('T')) {
        const [fromTime, toTime] = timeRange.split('|');
        console.log(`Using absolute time range filter: ${fromTime} to ${toTime}`);
        filters.push({
          range: {
            "@timestamp": {
              gte: fromTime,
              lte: toTime
            }
          }
        });
      } else {
        // Legacy relative time format (15m, 1h, etc.)
        console.log(`Using relative time range filter: now-${timeRange}`);
        filters.push({
          range: {
            "@timestamp": {
              gte: `now-${timeRange}`
            }
          }
        });
      }
    }

    return {
      bool: {
        must: baseQuery,
        filter: filters
      }
    };
  }

  /**
   * Parse search query with AND/OR operators and quoted terms
   * @param {string} query - Search query string
   * @returns {Object} OpenSearch query object
   */
  parseSearchQuery(query) {
    // For queries with AND/OR operators, parse them manually for exact matching
    if (query.toLowerCase().includes(' or ') || query.toLowerCase().includes(' and ')) {
      console.log('Detected AND/OR operators - parsing for exact matching');
      return this.parseComplexQuery(query);
    }

    // For simple queries, use the more controlled multi_match approach
    const searchFields = [
      "trace.messageid^3",      // Boost message ID heavily
      "trace.uri^2",            // Boost URI
      "message^1.5",            // Boost message content
      "event.original^1.5",     // Boost original event
      "trace.flow",             // Flow information
      "trace.appname",          // App name
      "trace.environment",      // Environment
      "trace.client_id",        // Client ID
      "trace.requestid",        // Request ID
      "trace.correlationid"     // Correlation ID
    ];

    // For simple queries, determine if it looks like a message ID or general search
    const cleanQuery = query.replace(/"/g, '').trim();
    const isMessageIdPattern = /^[a-zA-Z0-9\-_]+$/;
    const looksLikeMessageId = isMessageIdPattern.test(cleanQuery);

    console.log(`Query analysis: "${query}" -> clean: "${cleanQuery}" -> isMessageId: ${looksLikeMessageId} (length: ${cleanQuery.length})`);

    if (looksLikeMessageId && cleanQuery.length > 10) {
      // For message ID-like queries, use exact matching with high boost on message ID field
      console.log('Detected message ID pattern - using exact matching');
      return {
        bool: {
          should: [
            // Exact match on message ID field (highest priority) - try both analyzed and keyword
            {
              bool: {
                should: [
                  {
                    term: {
                      "trace.messageid": {
                        value: cleanQuery,
                        boost: 10
                      }
                    }
                  },
                  {
                    term: {
                      "trace.messageid.keyword": {
                        value: cleanQuery,
                        boost: 10
                      }
                    }
                  }
                ],
                minimum_should_match: 1
              }
            },
            // Exact phrase match in message content (medium priority)
            {
              match_phrase: {
                "message": {
                  query: cleanQuery,
                  boost: 3
                }
              }
            },
            // Exact phrase match in event.original (medium priority)
            {
              match_phrase: {
                "event.original": {
                  query: cleanQuery,
                  boost: 3
                }
              }
            },
            // Fallback: exact phrase in other fields (low priority)
            {
              multi_match: {
                query: cleanQuery,
                fields: ["trace.requestid^2", "trace.correlationid^2", "trace.uri"],
                type: "phrase",
                boost: 1
              }
            }
          ],
          minimum_should_match: 1
        }
      };
    } else {
      // For general text searches, use fuzzy matching
      console.log('General text search - using fuzzy matching');
      return {
        multi_match: {
          query: query,
          fields: searchFields,
          type: "best_fields",
          fuzziness: "AUTO",
          operator: "or"
        }
      };
    }
  }

  /**
   * Show no results message to user
   * @param {string} query - The search query that returned no results
   */
  showNoResultsMessage(query) {
    // Show no results message in the fileContent area (not the entire container)
    const processedContainer = document.getElementById('processedContainer');
    if (processedContainer) {
      processedContainer.classList.remove('hidden');
    }

    // Target the fileContent area specifically to preserve container structure
    const fileContent = document.getElementById('fileContent');
    if (fileContent) {
      fileContent.innerHTML = `
        <div class="text-center py-5 no-results-container">
          <div class="mb-4">
            <i class="bi bi-search no-results-icon" style="font-size: 4rem;"></i>
          </div>
          <h4 class="no-results-title mb-3">No Results Found</h4>
          <p class="no-results-text mb-4">
            No results found for query: <strong>"${query}"</strong>
          </p>
          <div class="no-results-suggestions">
            <p class="mb-2">Try adjusting your search criteria:</p>
            <ul class="list-unstyled">
              <li>• Check your spelling</li>
              <li>• Use different keywords</li>
              <li>• Expand the time range</li>
              <li>• Try a broader search</li>
            </ul>
          </div>
        </div>
      `;
    }

    console.warn('Search returned no results:', { query });
  }

  /**
   * Enhance entries with complete flows for each message ID
   * @param {Array} entries - Search result entries
   * @param {string} timeRange - Time range for search
   * @returns {Promise<Array>} Enhanced entries with complete flows
   */
  async enhanceWithCompleteFlows(entries, timeRange) {
    try {
      // Extract unique message IDs from search results
      const allMessageIds = [...new Set(entries.map(entry => entry.messageId).filter(id => id && id !== '-'))];

      if (allMessageIds.length === 0) {
        console.log('No message IDs found for enhancement');
        return entries;
      }

      // Limit the number of message IDs based on the requested flow count
      // This ensures we get the requested number of complete flows
      const maxFlows = this.maxFlowsFromOptions || 500; // Default to 500 if not set
      const messageIds = allMessageIds.slice(0, maxFlows);

      // Warn if we don't have enough unique flows to fulfill the request
      if (allMessageIds.length < maxFlows && allMessageIds.length > 0) {
        console.warn(`Only found ${allMessageIds.length} unique flows, but ${maxFlows} were requested. Consider expanding time range or using a broader search.`);
      }

      console.log(`Fetching complete flows for ${messageIds.length} message IDs (limited from ${allMessageIds.length} total):`, messageIds.slice(0, 5));

      // Build query to get ALL flows for these message IDs
      const completeFlowsQuery = {
        bool: {
          should: [
            {
              terms: {
                "trace.messageid": messageIds
              }
            },
            {
              terms: {
                "trace.messageid.keyword": messageIds
              }
            }
          ],
          minimum_should_match: 1,
          filter: [
            { term: { "logtype.keyword": "tracelog" } }
          ]
        }
      };

      // Add time range filter - use a wider range for complete flows to ensure we get all related flows
      if (timeRange && timeRange !== 'all') {
        // Check if timeRange contains ISO timestamps (from new time range picker)
        if (timeRange.includes('|') && timeRange.includes('T')) {
          const [fromTime, toTime] = timeRange.split('|');
          // For absolute time ranges, expand by 1 hour on each side
          const fromDate = new Date(fromTime);
          const toDate = new Date(toTime);
          fromDate.setHours(fromDate.getHours() - 1);
          toDate.setHours(toDate.getHours() + 1);

          console.log(`Expanding absolute time range for complete flows: ${fromTime} to ${toTime} → ${fromDate.toISOString()} to ${toDate.toISOString()}`);

          completeFlowsQuery.bool.filter.push({
            range: {
              "@timestamp": {
                gte: fromDate.toISOString(),
                lte: toDate.toISOString()
              }
            }
          });
        } else {
          // Legacy relative time format - expand the time range
          let expandedTimeRange = timeRange;
          if (timeRange === '15m') expandedTimeRange = '1h';
          else if (timeRange === '1h') expandedTimeRange = '4h';
          else if (timeRange === '4h') expandedTimeRange = '1d';
          else if (timeRange === '1d') expandedTimeRange = '3d';
          else if (timeRange === '7d') expandedTimeRange = '14d';

          console.log(`Expanding relative time range for complete flows: ${timeRange} → ${expandedTimeRange}`);

          completeFlowsQuery.bool.filter.push({
            range: {
              "@timestamp": {
                gte: `now-${expandedTimeRange}`
              }
            }
          });
        }
      }

      // Execute search for complete flows
      const completeFlowsResult = await opensearchClient.search(completeFlowsQuery, {
        size: 2000, // Large enough to get all flows for the message IDs
        sort: [{ '@timestamp': { order: 'asc' } }]
      });

      if (completeFlowsResult.success) {
        console.log('Complete flows search successful, transforming response...');
        const completeFlowsTransformed = opensearchTransformer.transformSearchResponse(completeFlowsResult.rawResponse);

        if (completeFlowsTransformed.success) {
          console.log(`Enhanced: ${entries.length} original → ${completeFlowsTransformed.entries.length} complete flows`);
          return completeFlowsTransformed.entries;
        } else {
          console.error('Failed to transform complete flows response:', completeFlowsTransformed.error);
        }
      } else {
        console.error('Complete flows search failed:', completeFlowsResult.error);
      }

      console.warn('Failed to enhance flows, using original entries');
      return entries;

    } catch (error) {
      console.error('Error enhancing flows:', error);
      return entries;
    }
  }

  /**
   * Clear UI elements when no results are found
   */
  clearUIForNoResults() {
    // Clear the file content area completely
    const fileContent = document.getElementById('fileContent');
    if (fileContent) {
      fileContent.innerHTML = '';
    }

    // Clear entry counter
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      entryCounter.textContent = '0 entries found';
    }

    // Clear any existing tables or views
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
      tableContainer.innerHTML = '';
    }

    // Clear processed container content
    const processedContainer = document.getElementById('processedContainer');
    if (processedContainer) {
      // Clear any existing content but keep the container visible
      const existingContent = processedContainer.querySelector('.container-fluid');
      if (existingContent) {
        existingContent.innerHTML = '';
      }
    }
  }

  /**
   * Show success notification using app toast
   */
  showSuccessNotification(message) {
    const toastElement = document.getElementById('liveToast');
    const toastBody = document.getElementById('toastMessage');

    if (toastElement && toastBody) {
      toastBody.textContent = message;

      // Set success style (green border)
      toastElement.classList.remove('border-danger');
      toastElement.classList.add('border-success');

      const toast = new bootstrap.Toast(toastElement);
      toast.show();
    } else {
      console.log(message);
    }
  }

  /**
   * Show error notification using app toast
   */
  showErrorNotification(message) {
    const toastElement = document.getElementById('liveToast');
    const toastBody = document.getElementById('toastMessage');

    if (toastElement && toastBody) {
      toastBody.textContent = message;

      // Set error style (red border)
      toastElement.classList.remove('border-success');
      toastElement.classList.add('border-danger');

      const toast = new bootstrap.Toast(toastElement);
      toast.show();
    } else {
      console.error(message);
    }
  }

  /**
   * Clear search input and related state
   */
  clearSearchState() {
    // Clear search input field
    const searchInput = document.getElementById('logSearchInput');
    if (searchInput) {
      searchInput.value = '';
    }

    // Reset global search state if it exists in window object
    if (window.globalSearchTerm !== undefined) {
      window.globalSearchTerm = '';
      window.globalSearchMatches = 0;
    }

    // Clear search match count
    const searchMatchCount = document.getElementById('searchMatchCount');
    if (searchMatchCount) {
      searchMatchCount.textContent = '0 matches';
    }

    // Disable search navigation buttons
    const prevMatchBtn = document.getElementById('prevMatchBtn');
    const nextMatchBtn = document.getElementById('nextMatchBtn');
    if (prevMatchBtn) prevMatchBtn.disabled = true;
    if (nextMatchBtn) nextMatchBtn.disabled = true;

    // Reset pagination to first page when performing new search
    stateManager.setPagination({ currentPage: 1 });
  }

  /**
   * Process a file
   * @param {string} action - The action to perform
   * @param {File} file - The file to process
   */
  processFile(action, file) {
    const reader = new FileReader();

    reader.onload = (event) => {
      this.handleContentProcessing(event.target.result, action, 'file');
    };

    reader.readAsText(file);
  }

  /**
   * Process text input
   * @param {string} action - The action to perform
   * @param {string} textInput - The text to process
   */
  processText(action, textInput) {
    this.handleContentProcessing(textInput, action, 'text input');
  }

  /**
   * Handle content processing based on action
   * @param {string} content - The content to process
   * @param {string} action - The action to perform
   * @param {string} source - The source of the content
   */
  handleContentProcessing(content, action, source = '') {
    console.log(`Processing ${source}:`, { action, contentLength: content.length });

    let processedContent = '';

    // Process content based on action
    if (action === 'extract') {
      console.log('Extracting message IDs');
      processedContent = this.extractMessageIds(content);
      stateManager.updateState('processedContent', processedContent);
    } else if (action === 'flow') {
      console.log('Processing log flow');
      this.processLogContent(content);
    }

    // Update UI
    if (action === 'extract') {
      // Display content directly instead of using uiManager to avoid circular dependency
      const fileContent = document.getElementById('fileContent');
      if (fileContent) {
        fileContent.innerHTML = processedContent;
        fileContent.classList.remove('hidden');
      }
    }

    // First collapse the input section
    const inputSection = document.getElementById('inputSection');
    const toggleInputButton = document.getElementById('toggleInputButton');
    if (inputSection && toggleInputButton) {
      // Collapse the input section
      inputSection.classList.add('collapsed');

      // Configure the toggle button
      toggleInputButton.classList.remove('d-none');
      toggleInputButton.classList.add('collapsed');
      toggleInputButton.setAttribute('title', 'Expand input section');

      // Make sure the button is visible and clickable
      toggleInputButton.style.display = 'inline-block';
      toggleInputButton.style.pointerEvents = 'auto';

      // Ensure the icon is correct - down arrow when collapsed
      const icon = toggleInputButton.querySelector('i');
      if (icon) {
        icon.className = 'bi bi-chevron-down';
        console.log('Setting icon to down arrow for collapsed state');
      }
    }

    // Get the processed container and show it
    const processedContainer = document.getElementById('processedContainer');
    processedContainer.classList.remove('hidden');

    // Set process type
    const header = document.querySelector('.log-entry-header');
    if (header) {
      header.dataset.processType = action;
    }

    // Reset formatting flag
    stateManager.updateState('isFormattedForOpenSearch', false);

    // Reset format button text and visibility
    const formatButton = document.getElementById('formatButton');
    if (formatButton) {
      if (action === 'extract') {
        // For extract action, show the button with default text
        formatButton.style.display = 'inline-flex';
        formatButton.querySelector('span').textContent = 'Format for OpenSearch';
      } else {
        // For other actions, hide the button
        formatButton.style.display = 'none';
      }
    }

    console.log('Content processing completed');
  }

  /**
   * Detect the log format type
   * @param {string} content - The log content
   * @returns {string} The detected format type ('standard', 'csv_with_path', 'simple_message', 'raw_json')
   */
  detectLogFormat(content) {
    // Check for the old standard format first - look for the date pattern followed by Row/Column markers
    // This is the most distinctive feature of the old format
    const oldFormatDatePattern = /(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d+, \d{4} @ \d+:\d+:\d+\.\d+Row: \d+, Column: \d+:/;
    if (oldFormatDatePattern.test(content)) {
      return 'standard';
    }

    // Check for CSV with path format - these are more specific checks for the CSV format
    if (content.trim().startsWith('log\\.file\\.path,message') ||
        content.trim().startsWith('log.file.path,message') ||
        // Check for CSV structure with the log file path at the beginning of lines
        (content.includes('/opt/apigee/var/log/') &&
         content.includes(',"{')) ||
        // Additional check for CSV format with single quotes
        (content.includes('/opt/apigee/var/log/') &&
         content.includes(',"{'))) {
      return 'csv_with_path';
    }

    // Check for simple message format - this is a format where each log entry starts with a JSON object
    // followed by sections for query string, headers, and message body
    if (content.trim().startsWith('message') ||
        content.includes('"{"') ||
        (content.trim().startsWith('{"time":') && content.includes('---------- Query string:')) ||
        (content.includes('{"time":') && content.includes('---------- Query string:') &&
         !content.includes('/opt/apigee/var/log/') && !content.includes('log.file.path'))) {
      return 'simple_message';
    }

    // Check for raw JSON format
    if (content.trim().startsWith('{') &&
        content.includes('"time":') &&
        content.includes('---------- Query string:')) {
      return 'raw_json';
    }

    // If we can't determine the format more specifically, check if it has JSON and log markers
    // This is a more generic check that should catch most Apigee log formats
    if (content.includes('{') &&
        content.includes('"time":') &&
        content.includes('"flow":') &&
        content.includes('---------- Query string:')) {
      // Look at structure to determine format
      if (content.includes('/opt/apigee/var/log/') && content.includes(',"{')) {
        return 'csv_with_path';
      } else {
        return 'standard';
      }
    }

    // Default to standard format if nothing else matches
    return 'standard';
  }

  /**
   * Extract JSON data from log entry based on format
   * @param {string} entry - The log entry
   * @param {string} format - The detected format
   * @returns {Object|null} The extracted JSON data or null if not found
   */
  extractJsonData(entry, format) {
    try {
      switch (format) {
        case 'standard':
          // Standard format: Extract JSON between curly braces followed by dashes
          // First, try to find JSON with the standard pattern
          const standardMatch = entry.match(/({.*?})\s*-{10}/);
          if (standardMatch) {
            return JSON.parse(standardMatch[1]);
          }

          // If that fails, try a more general approach for the old format
          // Use the same robust regex as for CSV format
          const jsonRegex = /{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*}/s;
          const jsonMatch = entry.match(jsonRegex);
          if (jsonMatch) {
            try {
              const jsonObj = JSON.parse(jsonMatch[0]);

              // Extract additional data if available
              const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
              if (queryStringMatch) {
                jsonObj._queryString = queryStringMatch[1].trim();
              }

              const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
              if (headersMatch) {
                jsonObj._headers = headersMatch[1].trim();
              }

              const messageBodyMatch = entry.match(/---------- Message body:\s*([\s\S]*?)(?=Row: \d+, Column: \d+:|$)/);
              if (messageBodyMatch) {
                // Remove trailing quotes if present
                let messageBody = messageBodyMatch[1].trim();

                // Handle escaped quotes in the message body
                if (messageBody.includes('""')) {
                  messageBody = messageBody.replace(/""/g, '"');
                }

                // Remove trailing quote if present
                if (messageBody.endsWith('"')) {
                  messageBody = messageBody.substring(0, messageBody.length - 1);
                }

                // Remove any remaining escaped quotes
                messageBody = messageBody.replace(/\\"/g, '"');

                jsonObj._messageBody = messageBody;
              }

              return jsonObj;
            } catch (e) {
              console.warn('Failed to parse JSON in standard format:', e);
            }
          }
          break;

        case 'csv_with_path':
          // CSV with path format: Extract JSON from the message column
          // Format: log.file.path,message or other CSV formats

          // First, check if this is a header row
          if (entry.trim().startsWith('log.file.path,message') ||
              entry.trim().startsWith('log\\.file\\.path,message') ||
              entry.trim().startsWith('message,')) {
            // Skip header rows
            return null;
          }

          try {
            // For CSV format, we need to handle the entire entry differently
            // The entry format is: /path/to/file.log,"{""json"":""data""} ... rest of log data

            // First, find the first comma which separates the path from the message
            const firstCommaIndex = entry.indexOf(',');
            if (firstCommaIndex === -1) {
              console.warn('CSV format entry does not contain a comma separator');
              return null;
            }

            // Extract the message part (everything after the first comma)
            let jsonStr = entry.substring(firstCommaIndex + 1).trim();

            // Check if the JSON part is wrapped in quotes
            if (jsonStr.startsWith('"') && (jsonStr.endsWith('"') || jsonStr.includes('"\n'))) {
              // Remove the outer quotes
              jsonStr = jsonStr.startsWith('"') ? jsonStr.substring(1) : jsonStr;

              // Find where the JSON string ends (it might not be at the end of the entry)
              const endQuoteIndex = jsonStr.indexOf('"\n');
              if (endQuoteIndex !== -1) {
                jsonStr = jsonStr.substring(0, endQuoteIndex);
              } else if (jsonStr.endsWith('"')) {
                jsonStr = jsonStr.substring(0, jsonStr.length - 1);
              }

              // Replace double escaped quotes with single quotes
              jsonStr = jsonStr.replace(/""/g, '"');

              // Extract the JSON object - use a more precise regex to match JSON objects
              // This regex matches a JSON object with proper nesting of braces
              const jsonRegex = /{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*}/s;
              const jsonMatch = jsonStr.match(jsonRegex);

              if (jsonMatch) {
                try {
                  // Try to parse the JSON object
                  const jsonObj = JSON.parse(jsonMatch[0]);

                  // Now we need to extract the HTTP headers and message body from the rest of the entry
                  // Store them in the JSON object for later use

                  // Extract query string
                  const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
                  if (queryStringMatch) {
                    jsonObj._queryString = queryStringMatch[1].trim();
                  }

                  // Extract HTTP headers
                  const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
                  if (headersMatch) {
                    jsonObj._headers = headersMatch[1].trim();
                  }

                  // Extract message body - handle both formats
                  // In CSV format, the message body might be followed by a quote and newline
                  const messageBodyMatch = entry.match(/---------- Message body:\s*([\s\S]*?)(?="\s*$|$)/);
                  if (messageBodyMatch) {
                    // Remove trailing quotes if present
                    let messageBody = messageBodyMatch[1].trim();

                    // Handle escaped quotes in the message body
                    if (messageBody.includes('""')) {
                      messageBody = messageBody.replace(/""/g, '"');
                    }

                    // Remove trailing quote if present
                    if (messageBody.endsWith('"')) {
                      messageBody = messageBody.substring(0, messageBody.length - 1);
                    }

                    // Remove any remaining escaped quotes
                    messageBody = messageBody.replace(/\\"/g, '"');

                    jsonObj._messageBody = messageBody;
                  }

                  return jsonObj;
                } catch (error) {
                  console.error('Error parsing JSON in CSV format:', error);
                }
              }
            }
          } catch (error) {
            console.error('Error processing CSV format entry:', error);
          }

          // Fallback to the original approach if the new approach fails
          try {
            // Handle CSV parsing with potential quoted fields
            let jsonStr = '';

            // Check if the entry has quotes that need special handling
            if (entry.includes('""')) {
              // This is likely a CSV with escaped quotes
              const csvParts = entry.split(',');
              if (csvParts.length >= 2) {
                // The message part might be wrapped in quotes and have escaped quotes inside
                jsonStr = csvParts.slice(1).join(',').trim();
                // Remove outer quotes if present
                if (jsonStr.startsWith('"') && jsonStr.endsWith('"')) {
                  jsonStr = jsonStr.substring(1, jsonStr.length - 1);
                }
                // Replace escaped quotes with regular quotes
                jsonStr = jsonStr.replace(/""/g, '"');
              }
            } else {
              // Simpler CSV format
              const csvParts = entry.split(',');
              if (csvParts.length >= 2) {
                jsonStr = csvParts.slice(1).join(',').trim();
                // Remove outer quotes if present
                if (jsonStr.startsWith('"') && jsonStr.endsWith('"')) {
                  jsonStr = jsonStr.substring(1, jsonStr.length - 1);
                }
              }
            }

            // Find the JSON object within the message
            if (jsonStr) {
              try {
                // Use the same robust regex as before
                const jsonRegex = /{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*}/s;
                const jsonMatch = jsonStr.match(jsonRegex);
                if (jsonMatch) {
                  const jsonObj = JSON.parse(jsonMatch[0]);

                  // Extract additional data if available
                  const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
                  if (queryStringMatch) {
                    jsonObj._queryString = queryStringMatch[1].trim();
                  }

                  const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
                  if (headersMatch) {
                    jsonObj._headers = headersMatch[1].trim();
                  }

                  const messageBodyMatch = entry.match(/---------- Message body:\s*([\s\S]*?)(?="\s*$|$)/);
                  if (messageBodyMatch) {
                    // Remove trailing quotes if present
                    let messageBody = messageBodyMatch[1].trim();

                    // Handle escaped quotes in the message body
                    if (messageBody.includes('""')) {
                      messageBody = messageBody.replace(/""/g, '"');
                    }

                    // Remove trailing quote if present
                    if (messageBody.endsWith('"')) {
                      messageBody = messageBody.substring(0, messageBody.length - 1);
                    }

                    // Remove any remaining escaped quotes
                    messageBody = messageBody.replace(/\\"/g, '"');

                    jsonObj._messageBody = messageBody;
                  }

                  return jsonObj;
                }
              } catch (innerError) {
                console.error('Error in JSON parsing fallback:', innerError);
              }
            }
          } catch (error) {
            console.error('Error in fallback CSV processing:', error);
          }
          break;

        case 'simple_message':
          // Simple message format: Extract JSON from the message
          // This is very similar to csv_with_path but without the path at the beginning
          try {
            // For apigee_structure2.txt format, extract values directly using regex
            if (entry.startsWith('message') || entry.startsWith('"{"')) {
              // Create a JSON object manually by extracting the key-value pairs
              try {
                // Extract the values using regex
                const timeMatch = entry.match(/""time"":\s*""([^""]*)""/) || [];
                const levelMatch = entry.match(/""level"":\s*""([^""]*)""/) || [];
                const messageidMatch = entry.match(/""messageid"":\s*""([^""]*)""/) || [];
                const flowMatch = entry.match(/""flow"":\s*""([^""]*)""/) || [];
                const flowstageMatch = entry.match(/""flowstage"":\s*""([^""]*)""/) || [];
                const requestidMatch = entry.match(/""requestid"":\s*""([^""]*)""/) || [];
                const correlationidMatch = entry.match(/""correlationid"":\s*""([^""]*)""/) || [];
                const uriMatch = entry.match(/""uri"":\s*""([^""]*)""/) || [];
                const verbMatch = entry.match(/""verb"":\s*""([^""]*)""/) || [];
                const clientIdMatch = entry.match(/""client_id"":\s*""([^""]*)""/) || [];
                const appNameMatch = entry.match(/""app_name"":\s*""([^""]*)""/) || [];
                const statuscodeMatch = entry.match(/""statuscode"":\s*""([^""]*)""/) || [];

                // Create a JSON object with the extracted values
                const jsonObj = {
                  time: timeMatch[1] || '',
                  level: levelMatch[1] || '',
                  messageid: messageidMatch[1] || '',
                  flow: flowMatch[1] || '',
                  flowstage: flowstageMatch[1] || '',
                  requestid: requestidMatch[1] || '',
                  correlationid: correlationidMatch[1] || '',
                  uri: uriMatch[1] || '',
                  verb: verbMatch[1] || '',
                  client_id: clientIdMatch[1] || '',
                  app_name: appNameMatch[1] || '',
                  statuscode: statuscodeMatch[1] || ''
                };

                // Extract additional data
                const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
                if (queryStringMatch) {
                  jsonObj._queryString = queryStringMatch[1].trim();
                }

                const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
                if (headersMatch) {
                  jsonObj._headers = headersMatch[1].trim();
                }

                // For apigee_structure2.txt format, the message body is followed by a quote and then a newline
                // The pattern is: "---------- Message body:\n...\n    \""
                const messageBodyMatch = entry.match(/---------- Message body:\s*([\s\S]*?)(?=\s*"\s*$|\s*"{\s*|$)/);
                if (messageBodyMatch) {
                  // Get the raw message body
                  let messageBody = messageBodyMatch[1].trim();

                  // If the message body is empty or just whitespace, set it to empty string
                  if (!messageBody || messageBody === '"') {
                    messageBody = '';
                  } else {
                    // Handle escaped quotes in the message body
                    if (messageBody.includes('""')) {
                      messageBody = messageBody.replace(/""/g, '"');
                    }

                    // Remove trailing quote if present
                    if (messageBody.endsWith('"')) {
                      messageBody = messageBody.substring(0, messageBody.length - 1);
                    }

                    // Remove any remaining escaped quotes
                    messageBody = messageBody.replace(/\\"/g, '"');
                  }

                  jsonObj._messageBody = messageBody;
                }

                return jsonObj;
              } catch (regexError) {
                console.error('Error extracting values with regex:', regexError);
              }
            } else {
              // This is a standard JSON format
              // The JSON object is at the start of the entry and ends at the first occurrence of "---------- Query string:"
              const jsonEndIndex = entry.indexOf('---------- Query string:');
              if (jsonEndIndex > 0) {
                const jsonStr = entry.substring(0, jsonEndIndex).trim();

                try {
                  // Parse the JSON object
                  const jsonObj = JSON.parse(jsonStr);

                  // Extract additional data
                  const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
                  if (queryStringMatch) {
                    jsonObj._queryString = queryStringMatch[1].trim();
                  }

                  const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
                  if (headersMatch) {
                    jsonObj._headers = headersMatch[1].trim();
                  }

                  // For standard JSON format, use the same improved message body extraction
                  const messageBodyMatch = entry.match(/---------- Message body:\s*([\s\S]*?)(?=\s*"\s*$|\s*"{\s*|$)/);
                  if (messageBodyMatch) {
                    // Get the raw message body
                    let messageBody = messageBodyMatch[1].trim();

                    // If the message body is empty or just whitespace, set it to empty string
                    if (!messageBody || messageBody === '"') {
                      messageBody = '';
                    } else {
                      // Handle escaped quotes in the message body
                      if (messageBody.includes('""')) {
                        messageBody = messageBody.replace(/""/g, '"');
                      }

                      // Remove trailing quote if present
                      if (messageBody.endsWith('"')) {
                        messageBody = messageBody.substring(0, messageBody.length - 1);
                      }

                      // Remove any remaining escaped quotes
                      messageBody = messageBody.replace(/\\"/g, '"');
                    }

                    jsonObj._messageBody = messageBody;
                  }

                  return jsonObj;
                } catch (jsonError) {
                  console.error('Error parsing JSON in simple message format:', jsonError);
                }
              }
            }
          } catch (error) {
            console.error('Error processing simple message format:', error);
          }
          break;

        case 'raw_json':
          // Raw JSON format: The entry itself is JSON
          try {
            // Just parse the entire entry
            const jsonObj = JSON.parse(entry);

            // Extract additional data if available
            const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
            if (queryStringMatch) {
              jsonObj._queryString = queryStringMatch[1].trim();
            }

            const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
            if (headersMatch) {
              jsonObj._headers = headersMatch[1].trim();
            }

            const messageBodyMatch = entry.match(/---------- Message body:\s*([\s\S]*?)(?=$)/);
            if (messageBodyMatch) {
              // Remove trailing quotes if present
              let messageBody = messageBodyMatch[1].trim();

              // Handle escaped quotes in the message body
              if (messageBody.includes('""')) {
                messageBody = messageBody.replace(/""/g, '"');
              }

              // Remove trailing quote if present
              if (messageBody.endsWith('"')) {
                messageBody = messageBody.substring(0, messageBody.length - 1);
              }

              // Remove any remaining escaped quotes
              messageBody = messageBody.replace(/\\"/g, '"');

              jsonObj._messageBody = messageBody;
            }

            return jsonObj;
          } catch (error) {
            console.error('Error processing raw JSON format:', error);

            // Try to extract JSON using regex as a fallback
            try {
              const jsonRegex = /{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*}/s;
              const jsonMatch = entry.match(jsonRegex);
              if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
              }
            } catch (fallbackError) {
              console.error('Error in raw JSON fallback:', fallbackError);
            }
          }

        default:
          console.warn(`Unknown format: ${format}`);
          return null;
      }
    } catch (error) {
      console.error(`Error extracting JSON data for format ${format}:`, error);
      return null;
    }

    return null;
  }

  /**
   * Process log content and extract structured data
   * @param {string} content - The log content to process
   */
  processLogContent(content) {
    // Detect the log format
    const format = this.detectLogFormat(content);
    console.log(`Detected log format: ${format}`);

    // Determine how to split the content based on the detected format
    let logEntries = [];

    if (format === 'standard') {
      // For standard format, split by date pattern that separates logs
      const datePattern = /(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d+, \d{4} @ \d+:\d+:\d+\.\d+Row: \d+, Column: \d+:/;

      // Check if we have the date pattern in the content
      if (datePattern.test(content)) {
        // Split and filter entries - maintain raw log order (newest to oldest)
        logEntries = content.split(datePattern)
          .filter(entry => entry.trim() !== '');

        // If the first entry is empty (which happens when the content starts with the date pattern),
        // remove it to avoid processing an empty entry
        if (logEntries.length > 0 && logEntries[0].trim() === '') {
          logEntries.shift();
        }

        // Add the date back to each entry for proper processing
        const dates = content.match(datePattern);
        if (dates && dates.length > 0 && logEntries.length === dates.length) {
          // Combine each date with its corresponding entry
          logEntries = logEntries.map((entry, index) => dates[index] + entry);
        }
      } else {
        // If no date pattern is found but format is still 'standard',
        // treat the entire content as a single entry
        logEntries = [content];
      }
    } else if (format === 'csv_with_path') {
      // For CSV format, we need to handle the entries differently
      // Each log entry starts with a path and spans multiple lines

      // First, filter out the header line
      const contentWithoutHeader = content
        .replace(/^log\.file\.path,message\s*\n/m, '')
        .replace(/^log\\.file\\.path,message\s*\n/m, '');

      // Split the content by lines that start with the log path pattern
      const logPathPattern = /^\/opt\/apigee\/var\/log\//m;

      // Find all starting positions of log entries
      const startPositions = [];
      let match;
      const regex = new RegExp(logPathPattern, 'gm');

      while ((match = regex.exec(contentWithoutHeader)) !== null) {
        startPositions.push(match.index);
      }

      // Extract each log entry based on start positions
      logEntries = [];
      for (let i = 0; i < startPositions.length; i++) {
        const start = startPositions[i];
        const end = i < startPositions.length - 1 ? startPositions[i + 1] : contentWithoutHeader.length;
        const entry = contentWithoutHeader.substring(start, end).trim();
        if (entry) {
          logEntries.push(entry);
        }
      }

      // If no entries were found using the advanced method, fall back to the simple method
      if (logEntries.length === 0) {
        logEntries = content.split('\n')
          .filter(line => line.trim() !== '' &&
                  !line.trim().startsWith('log.file.path,message') &&
                  !line.trim().startsWith('log\\.file\\.path,message'));
      }
    } else if (format === 'simple_message') {
      // For simple message format, we need to handle the entries differently
      // Each log entry starts with either 'message' or a JSON object

      // Split by the pattern that indicates the start of a new entry
      // This handles both the quoted JSON format (like in apigee_structure2.txt) and the standard format
      const entryStartPattern = /^(message|\s*"{|\s*{)/m;

      // Find all starting positions of log entries
      const startPositions = [];
      let match;
      const regex = new RegExp(entryStartPattern, 'gm');

      while ((match = regex.exec(content)) !== null) {
        startPositions.push(match.index);
      }

      // Extract each log entry based on start positions
      logEntries = [];
      for (let i = 0; i < startPositions.length; i++) {
        const start = startPositions[i];
        const end = i < startPositions.length - 1 ? startPositions[i + 1] : content.length;
        const entry = content.substring(start, end).trim();
        if (entry) {
          logEntries.push(entry);
        }
      }

      // If no entries were found using the advanced method, fall back to the simple method
      if (logEntries.length === 0) {
        logEntries = content.split('\n').filter(line => line.trim() !== '');
      }
    } else {
      // For other formats, split by newlines
      logEntries = content.split('\n').filter(line => line.trim() !== '');
    }

    const entries = []; // Reset entries array for new content

    logEntries.forEach((entry, originalIndex) => {
      try {
        // Store original entry for message body extraction before removing row/column markers
        const originalEntry = entry;

        // Remove "Row: X, Column: Y:" patterns from the entry
        entry = entry.replace(/Row: \d+, Column: \d+:/g, '').trim();

        // Extract environment from the path - keep this the same as current implementation
        const envMatch = entry.match(/\/messagelogging\/([^\/]+)/);
        const env = envMatch ? envMatch[1] : '-';

        // Extract JSON data based on format
        const logEntry = this.extractJsonData(entry, format);
        if (logEntry) {
          // Store original entry with original index
          const formattedTime = logEntry.time ? this.formatDate(logEntry.time) : '-';
          const flow = logEntry.flow || '-';
          const uri = logEntry.uri || '-';
          const appName = logEntry.app_name || '-';
          const statusCode = logEntry.statuscode || '-';
          const messageId = logEntry.messageid || '-';
          const verb = logEntry.verb || '-';

          // Check if we have pre-extracted data from the CSV format
          let messageBody = 'No message body available';
          let headers = 'No headers available';
          let queryString = 'Not available';

          if (logEntry._messageBody) {
            // Use the pre-extracted message body from the CSV or simple message format
            messageBody = logEntry._messageBody;
          } else {
            // Extract message body from the originalEntry to properly respect row/column markers
            // Use a pattern that captures everything after "---------- Message body:" until "Row: X, Column: Y:"
            const messageBodyMatch = originalEntry.match(/---------- Message body:\s*([\s\S]*?)(?=Row: \d+, Column: \d+:|$)/);

            if (messageBodyMatch) {
              // Get the raw message body
              messageBody = messageBodyMatch[1].trim();

              // If the message body is empty or just whitespace or just a quote, set it to empty string
              if (!messageBody || messageBody === '"') {
                messageBody = '';
              }

              // For apigee_structure2.txt format, the message body might end with a quote
              if (messageBody.endsWith('"')) {
                messageBody = messageBody.substring(0, messageBody.length - 1).trim();
              }
            } else {
              messageBody = 'No message body available';
            }
          }

          // Format message body if it contains XML or JSON
          if (messageBody && messageBody !== 'No message body available') {
            messageBody = this.wrapMessageBody(messageBody);
          }

          if (logEntry._headers) {
            // Use the pre-extracted headers from the CSV format
            headers = logEntry._headers;
          } else {
            // Extract HTTP Headers and format them properly
            const headersMatch = entry.match(/---------- HTTPHeaders:\s*([\s\S]*?)(?=---------- Message body:|$)/);
            headers = headersMatch ? headersMatch[1].trim() : 'No headers available';
          }

          // Format headers with each header on a new line
          if (headers && headers !== 'No headers available') {
            headers = this.formatHTTPHeaders(headers, { preserveOriginal: true, format: format });
          }

          if (logEntry._queryString) {
            // Use the pre-extracted query string from the CSV format
            queryString = logEntry._queryString;
          } else {
            // Extract Query String if available
            const queryStringMatch = entry.match(/---------- Query string:\s*([\s\S]*?)(?=---------- HTTPHeaders:|$)/);
            queryString = queryStringMatch ? queryStringMatch[1].trim() : 'Not available';
          }

          // Extract client_id from the log entry if available
          const clientId = logEntry.client_id || '';

          // Add entry to array
          entries.push({
            time: formattedTime,
            flow,
            flowstage: logEntry.flowstage || '',
            level: logEntry.level || '',
            uri,
            appName,
            statusCode,
            env,
            messageId,
            requestId: logEntry.requestid || '',
            correlationId: logEntry.correlationid || '',
            verb,
            headers,
            messageBody,
            client_id: clientId,
            queryString: queryString,
            rawPosition: originalIndex,  // Store original raw log position
            format: format  // Store the detected format for later use
          });
        }
      } catch (error) {
        console.error(`Error processing log entry ${originalIndex}:`, error);
      }
    });

    // Sort entries by time and reverse for display (oldest to newest)
    entries.sort((a, b) => new Date(b.time) - new Date(a.time)); // Sort newest to oldest
    entries.reverse(); // Reverse to display oldest to newest

    // Update state with new entries
    stateManager.setEntries(entries);

    // Initialize filter states with the new entries
    this.initializeFilterStates(entries);

    // Update UI based on current view type
    // We'll use a custom event to notify the UI to update
    // This avoids circular dependencies with uiManager
    const currentViewType = stateManager.getState().currentViewType;
    const event = new CustomEvent('entriesProcessed', {
      detail: { viewType: currentViewType }
    });
    document.dispatchEvent(event);
  }

  /**
   * Initialize filter states with new entries
   * @param {Array} entries - The log entries (not used directly but kept for API consistency)
   */
  initializeFilterStates(_entries) {
    // Initialize filter states without needing to extract message IDs and flows
    // We'll just set up the default state

    // Initialize both flow and call view filter states
    const newFilterState = {
      flows: {
        selectedMessageIds: [],
        allMessageIds: true,
        selectedFlows: [],
        allFlows: true,
        selectedStatusCodes: [],
        allStatusCodes: true
      },
      calls: {
        selectedMessageIds: [],
        allMessageIds: true,
        selectedFlows: [],
        allFlows: true,
        selectedStatusCodes: [],
        allStatusCodes: true
      }
    };

    // Update filter state
    stateManager.updateState('filterState', newFilterState);

    // Reset column visibility state to default (all columns visible)
    const columnVisibilityState = {
      flows: {
        'allColumns': true,
        'selectedColumns': [],
        'time': true,
        'env-org': true,
        'message-id': true,
        'flow': true,
        'app-name': true,
        'uri': true,
        'status-code': true
      },
      calls: {
        'allColumns': true,
        'selectedColumns': [],
        'time': true,
        'env-org': true,
        'message-id': true,
        'method': true,
        'uri': true,
        'response-time': true,
        'status-code': true
      }
    };

    // Update column visibility state
    stateManager.updateState('columnVisibilityState', columnVisibilityState);

    // Update UI filter indicators
    setTimeout(() => {
      // Use setTimeout to ensure this runs after the UI has been updated
      const event = new CustomEvent('filterStateReset');
      document.dispatchEvent(event);
    }, 0);
  }

  /**
   * Mask sensitive tokens in text
   * @param {string} content - The content to mask
   * @returns {string} The masked content
   */
  maskTokens(content) {
    // Use the centralized maskContent utility
    return maskContent(content);
  }

  /**
   * Extract message IDs from content
   * @param {string} content - The content to extract from
   * @returns {string} Newline-separated list of message IDs
   */
  extractMessageIds(content) {
    // Clear search input and related state when extracting message IDs
    this.clearSearchState();

    const messageIdPattern = /""?messageid""?:\s*""([^""]+)""|"?messageid"?:\s*"([^"]+)"/g;
    const uniqueValues = new Set();
    let match;

    while ((match = messageIdPattern.exec(content)) !== null) {
      uniqueValues.add(match[1] || match[2]);
    }

    const uniqueCount = uniqueValues.size;

    // Update entry counter with message ID count
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      entryCounter.textContent = `${uniqueCount} unique message ${uniqueCount === 1 ? 'ID' : 'IDs'} found`;
    }

    return Array.from(uniqueValues).join('\n');
  }

  /**
   * Format HTTP headers
   * @param {string} headersStr - The headers string
   * @param {Object} options - Options for formatting
   * @returns {string} Formatted headers
   */
  formatHTTPHeaders(headersStr, options = {}) {
    if (!headersStr || headersStr === 'No headers available.') return headersStr;

    // Handle different line endings and normalize to \n
    headersStr = headersStr.replace(/\r\n|\r/g, '\n');

    // Check if this is a new format log entry
    const isNewFormat = options.format &&
                       (options.format === 'csv_with_path' ||
                        options.format === 'simple_message' ||
                        options.format === 'raw_json');

    // Check if headers are already properly formatted (one header per line)
    const lines = headersStr.split('\n').filter(line => line.trim() !== '');
    const properlyFormatted = lines.every(line => {
      const colonIndex = line.indexOf(':');
      return colonIndex > 0 && colonIndex < line.length - 1;
    });

    // If headers are already properly formatted and it's a new format, just apply masking if needed
    if (properlyFormatted && isNewFormat) {
      // Apply masking if needed
      if ((options.mask || stateManager.getState().maskSensitiveData) && !options.preserveOriginal) {
        return lines.map(line => {
          const colonIndex = line.indexOf(':');
          const headerName = line.substring(0, colonIndex).trim();
          let headerValue = line.substring(colonIndex + 1).trim();
          headerValue = maskSensitiveData(headerValue, headerName.toLowerCase());
          return `${headerName}: ${headerValue}`;
        }).join('\n');
      }
      return headersStr;
    }

    // Extract headers section if the input has section markers
    const startMarker = "---------- HTTPHeaders:";
    const endMarker = "---------- Message body:";
    let headerContent = headersStr;

    if (typeof headersStr === 'string' && headersStr.includes(startMarker)) {
      const startIndex = headersStr.indexOf(startMarker);
      if (startIndex === -1) return headersStr;

      const headerSection = headersStr.substring(startIndex + startMarker.length);
      const endIndex = headerSection.indexOf(endMarker);
      headerContent = endIndex === -1
        ? headerSection.trim()
        : headerSection.substring(0, endIndex).trim();
    }

    // DIRECT APPROACH: Split the content by header pattern
    // First, normalize spaces after colons for consistency
    headerContent = headerContent.replace(/:/g, ': ');

    // Split the content by header pattern
    const headerNames = headerContent.match(/\b[A-Za-z][-A-Za-z0-9]*:/g) || [];
    const headers = [];

    if (headerNames.length > 0) {
      // Process each header name
      for (let i = 0; i < headerNames.length; i++) {
        const headerName = headerNames[i].substring(0, headerNames[i].length - 1); // Remove the colon

        // Find where this header starts in the content
        const headerStart = headerContent.indexOf(headerNames[i]);

        // Find where this header ends (at the next header or end of content)
        let headerEnd;
        if (i < headerNames.length - 1) {
          headerEnd = headerContent.indexOf(headerNames[i + 1]);
        } else {
          headerEnd = headerContent.length;
        }

        // Extract the header value
        const headerValue = headerContent.substring(headerStart + headerNames[i].length, headerEnd).trim();

        // Add the header with proper formatting
        headers.push(`${headerName}: ${headerValue}`);
      }
    } else {
      // Fallback if no headers were found
      headers.push(headerContent.trim());
    }

    // Process each header for masking if needed
    const processedHeaders = headers.map(header => {
      const colonIndex = header.indexOf(':');
      if (colonIndex > 0) {
        const headerName = header.substring(0, colonIndex).trim();
        let headerValue = header.substring(colonIndex + 1).trim();

        // Apply masking based on options or state
        if ((options.mask || stateManager.getState().maskSensitiveData) && !options.preserveOriginal) {
          // Use the centralized maskSensitiveData utility
          headerValue = maskSensitiveData(headerValue, headerName.toLowerCase());
        }
        return `${headerName}: ${headerValue}`;
      }
      return header;
    });

    // Join headers with newlines - ENSURE EACH HEADER IS ON ITS OWN LINE
    const processedResult = processedHeaders.join('\n');

    return processedResult;
  }

  /**
   * Mask header value
   * @param {string} headerName - The header name
   * @param {string} headerValue - The header value
   * @param {Object} options - Options for masking
   * @returns {string} The masked header value
   */
  maskHeaderValue(headerName, headerValue, options = {}) {
    if ((!options.mask && !stateManager.getState().maskSensitiveData) || options.preserveOriginal) return headerValue;

    // Use the centralized maskSensitiveData utility
    return maskSensitiveData(headerValue, headerName.toLowerCase());
  }

  /**
   * Colorize header keys in HTTP headers
   * @param {string} headers - The headers string
   * @param {Object} options - Options for formatting
   * @returns {string} HTML with colorized header keys
   */
  colorizeHeaderKeys(headers, options = {}) {
    if (!headers || headers === 'No headers available') return headers;

    // Check if the headers are already in HTML format
    if (headers.includes('<span class="http-header-name">') || headers.includes('<span class="header-key">')) {
      return headers;
    }

    // Determine which CSS class to use based on format
    // For new formats (csv_with_path, simple_message, raw_json), use http-header-name
    // For old format, use header-key to maintain original styling
    const isNewFormat = options.format &&
                       (options.format === 'csv_with_path' ||
                        options.format === 'simple_message' ||
                        options.format === 'raw_json');

    const headerKeyClass = isNewFormat ? "http-header-name" : "header-key";

    // Split headers by line or <br> tags
    const separator = headers.includes('<br>') ? '<br>' : '\n';
    const lines = headers.split(separator);

    // Colorize each line
    const colorizedLines = lines.map(line => {
      // Skip empty lines
      if (!line.trim()) return line;

      // Skip lines that are already formatted with spans
      if (line.includes('<span')) return line;

      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex);
        const value = line.substring(colonIndex + 1).trim();

        // Use the appropriate class based on format
        return `<span class="${headerKeyClass}">${key}</span>: <span class="http-header-value" data-original="${value}">${value}</span>`;
      }
      return line;
    });

    // Join with the same separator that was used to split
    return colorizedLines.join(separator);
  }

  /**
   * Wrap message body for display
   * @param {string} message - The message body
   * @returns {string} The original message body without any modifications
   */
  wrapMessageBody(message) {
    // Return the message as is, without any modifications
    return message;
  }

  /**
   * Format date as YYYY-MM-DD HH:MM:SS.SSS
   * @param {string} dateString - The date string to format
   * @returns {string} The formatted date
   */
  formatDate(dateString) {
    const date = new Date(dateString);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  /**
   * Calculate response time between request and response flows
   * @param {Array} callEntries - The call entries
   * @returns {string} The response time
   */
  calculateResponseTime(callEntries) {
    const proxyReqFlows = callEntries.filter(entry => entry.flow === 'PROXY_REQ_FLOW');
    if (proxyReqFlows.length === 0) return '-';

    // Get the first proxy request flow for start time
    const firstProxyReqFlow = proxyReqFlows[0];
    const t_proxy_req = new Date(firstProxyReqFlow.time.replace(' ', 'T')).getTime();

    // 1. Try PROXY_REQ_FLOW to PROXY_RESP_FLOW
    const proxyRespFlow = callEntries.find(entry => entry.flow === 'PROXY_RESP_FLOW');
    if (proxyRespFlow) {
      const t_proxy_resp = new Date(proxyRespFlow.time.replace(' ', 'T')).getTime();
      return ((t_proxy_resp - t_proxy_req) / 1000).toFixed(3) + " s";
    }

    // 2. Try PROXY_REQ_FLOW to TARGET_RESP_FLOW
    const targetRespFlow = callEntries.find(entry => entry.flow === 'TARGET_RESP_FLOW');
    if (targetRespFlow) {
      const t_target_resp = new Date(targetRespFlow.time.replace(' ', 'T')).getTime();
      return ((t_target_resp - t_proxy_req) / 1000).toFixed(3) + " s";
    }

    // 3. Check for multiple TARGET_REQ_FLOW entries
    const targetReqFlows = callEntries.filter(entry => entry.flow === 'TARGET_REQ_FLOW');
    if (targetReqFlows.length > 1) {
      const lastTargetReq = targetReqFlows[targetReqFlows.length - 1];
      if (lastTargetReq.statusCode) {
        const t_last_target_req = new Date(lastTargetReq.time.replace(' ', 'T')).getTime();
        return ((t_last_target_req - t_proxy_req) / 1000).toFixed(3) + " s";
      }
    }

    // 4. Check for multiple PROXY_REQ_FLOW entries (new case)
    if (proxyReqFlows.length > 1) {
      // Sort by time, newest first
      const sortedProxyReqFlows = [...proxyReqFlows].sort((a, b) => new Date(b.time) - new Date(a.time));
      const lastProxyReq = sortedProxyReqFlows[0]; // Get the newest one

      if (lastProxyReq.statusCode && lastProxyReq.statusCode !== '-') {
        const t_last_proxy_req = new Date(lastProxyReq.time.replace(' ', 'T')).getTime();
        return ((t_last_proxy_req - t_proxy_req) / 1000).toFixed(3) + " s";
      }
    }

    // 5. No valid response time can be calculated
    return '-';
  }

  /**
   * Aggregate entries by call
   * @param {Array} filteredEntries - The filtered entries
   * @returns {Array} The aggregated calls
   */
  aggregateEntriesByCall(filteredEntries) {
    // Group entries by messageId
    const messageGroups = {};
    filteredEntries.forEach(entry => {
      if (!messageGroups[entry.messageId]) {
        messageGroups[entry.messageId] = [];
      }
      messageGroups[entry.messageId].push(entry);
    });

    // Process each message group
    const calls = [];
    Object.entries(messageGroups).forEach(([messageId, entries]) => {
      // Find flows for basic info and status code
      const proxyReqFlows = entries.filter(entry => entry.flow === 'PROXY_REQ_FLOW');
      const proxyReqFlow = proxyReqFlows.length > 0 ? proxyReqFlows[0] : null;
      const proxyRespFlow = entries.find(entry => entry.flow === 'PROXY_RESP_FLOW');
      const targetRespFlow = entries.find(entry => entry.flow === 'TARGET_RESP_FLOW');
      const targetReqFlows = entries.filter(entry => entry.flow === 'TARGET_REQ_FLOW');

      // Calculate response time
      const responseTime = this.calculateResponseTime(entries);

      // Helper function to check if a status code is valid
      const isValidStatusCode = (code) => code && code !== '-' && code !== '';

      // Get status code in order of precedence
      let statusCode = '-';
      if (proxyRespFlow && isValidStatusCode(proxyRespFlow.statusCode)) {
        // First priority: PROXY_RESP_FLOW
        statusCode = proxyRespFlow.statusCode;
      } else if (targetRespFlow && isValidStatusCode(targetRespFlow.statusCode)) {
        // Second priority: TARGET_RESP_FLOW
        statusCode = targetRespFlow.statusCode;
      } else if (targetReqFlows.length > 0) {
        // Third priority: Last TARGET_REQ_FLOW with status code (by time)
        const targetReqWithStatus = targetReqFlows
          .sort((a, b) => new Date(b.time) - new Date(a.time)) // Sort by time, newest first
          .find(entry => isValidStatusCode(entry.statusCode));
        if (targetReqWithStatus) {
          statusCode = targetReqWithStatus.statusCode;
        }
      } else if (proxyReqFlows.length > 1) {
        // Fourth priority: Check for multiple PROXY_REQ_FLOW entries
        // Sort by time, newest first to get the latest one
        const sortedProxyReqFlows = [...proxyReqFlows].sort((a, b) => new Date(b.time) - new Date(a.time));
        const proxyReqWithStatus = sortedProxyReqFlows.find(entry => isValidStatusCode(entry.statusCode));
        if (proxyReqWithStatus) {
          statusCode = proxyReqWithStatus.statusCode;
        }
      }

      // Create call object with debugging info
      const call = {
        messageId: messageId,
        time: proxyReqFlow ? proxyReqFlow.time : '-',
        env: proxyReqFlow ? proxyReqFlow.env : '-',
        httpMethod: proxyReqFlow ? proxyReqFlow.verb : '-',
        uri: proxyReqFlow ? proxyReqFlow.uri : '-',
        statusCode: statusCode,
        responseTime: responseTime,
        originalEntries: entries // Include original entries for debugging
      };
      calls.push(call);
    });

    return calls;
  }
  /**
   * Parse complex queries with AND/OR operators for exact matching
   * @param {string} query - Query with AND/OR operators
   * @returns {Object} OpenSearch query object
   */
  parseComplexQuery(query) {
    // Extract quoted terms and their positions
    const quotedTerms = [];
    let processedQuery = query.replace(/"([^"]+)"/g, (match, term) => {
      quotedTerms.push(term.trim());
      return `__QUOTED_${quotedTerms.length - 1}__`;
    });

    // Split by OR first (lower precedence)
    const orParts = processedQuery.split(/\s+or\s+/i);

    if (orParts.length > 1) {
      console.log('Processing OR query with parts:', orParts);
      // Handle OR query - each part should be an exact match
      const shouldClauses = orParts.map(part => {
        const cleanPart = part.replace(/__QUOTED_(\d+)__/g, (match, index) => {
          return quotedTerms[parseInt(index)];
        }).trim();

        return this.createExactMatchQuery(cleanPart);
      });

      return {
        bool: {
          should: shouldClauses,
          minimum_should_match: 1
        }
      };
    } else {
      // Handle AND query
      const andParts = processedQuery.split(/\s+and\s+/i);

      if (andParts.length > 1) {
        console.log('Processing AND query with parts:', andParts);
        const mustClauses = andParts.map(part => {
          const cleanPart = part.replace(/__QUOTED_(\d+)__/g, (match, index) => {
            return quotedTerms[parseInt(index)];
          }).trim();

          return this.createExactMatchQuery(cleanPart);
        });

        return {
          bool: {
            must: mustClauses
          }
        };
      }
    }

    // Fallback to single term
    return this.createExactMatchQuery(query.replace(/"/g, '').trim());
  }

  /**
   * Create exact match query for a single term (used in complex queries)
   * @param {string} term - Search term
   * @returns {Object} OpenSearch query object
   */
  createExactMatchQuery(term) {
    const isMessageIdPattern = /^[a-zA-Z0-9\-_]+$/;
    const looksLikeMessageId = isMessageIdPattern.test(term);

    if (looksLikeMessageId && term.length > 10) {
      // For message ID-like terms, prioritize exact match on message ID field
      return {
        bool: {
          should: [
            {
              term: {
                "trace.messageid": {
                  value: term,
                  boost: 10
                }
              }
            },
            {
              term: {
                "trace.requestid": {
                  value: term,
                  boost: 5
                }
              }
            },
            {
              match_phrase: {
                "message": {
                  query: term,
                  boost: 2
                }
              }
            },
            {
              match_phrase: {
                "event.original": {
                  query: term,
                  boost: 2
                }
              }
            }
          ],
          minimum_should_match: 1
        }
      };
    } else {
      // For general terms, use phrase matching
      return {
        multi_match: {
          query: term,
          fields: [
            "trace.messageid^3",
            "trace.uri^2",
            "message^1.5",
            "event.original^1.5",
            "trace.flow",
            "trace.appname"
          ],
          type: "phrase"
        }
      };
    }
  }

  /**
   * Build structured query for advanced search with special handling
   * @param {string} query - Query string with special syntax
   * @param {Array} filters - Base filters
   * @returns {Object} OpenSearch query object
   */
  buildAdvancedStructuredQuery(query, filters) {
    const mustClauses = [];
    const shouldClauses = [];

    // Handle URI wildcard queries directly (no special placeholder needed)
    const uriMatch = query.match(/trace\.uri:\*([^*\s]+)\*/);
    if (uriMatch) {
      const uriValue = uriMatch[1];
      // Use wildcard query for URI (single field, more efficient)
      mustClauses.push({
        wildcard: {
          "trace.uri": {
            value: `*${uriValue}*`,
            case_insensitive: true
          }
        }
      });
    }

    // Handle message ID queries
    if (query.includes('trace.messageid:')) {
      const messageIdMatch = query.match(/trace\.messageid:"([^"]+)"/);
      if (messageIdMatch) {
        const messageId = messageIdMatch[1];
        mustClauses.push({
          term: { "apiproxy.messageid": messageId }
        });
      }
    }

    // Handle message body queries with parentheses
    if (query.includes('(message:') && query.includes('event.original:')) {
      const messageBodyMatch = query.match(/\(message:\*([^*]+)\*\s+OR\s+event\.original:\*([^*]+)\*\)/);
      if (messageBodyMatch) {
        const searchTerm = messageBodyMatch[1];
        mustClauses.push({
          bool: {
            should: [
              {
                wildcard: {
                  "message": {
                    value: `*${searchTerm}*`,
                    case_insensitive: true
                  }
                }
              },
              {
                wildcard: {
                  "event.original": {
                    value: `*${searchTerm}*`,
                    case_insensitive: true
                  }
                }
              }
            ],
            minimum_should_match: 1
          }
        });
      }
    }

    // Handle other field queries
    const fieldMatches = query.match(/(\w+\.?\w*):([^\s]+)/g);
    if (fieldMatches) {
      fieldMatches.forEach(match => {
        if (!match.includes('trace.uri:*') && !match.includes('trace.messageid') && !match.includes('message:') && !match.includes('event.original:')) {
          const [field, value] = match.split(':');
          const cleanValue = value.replace(/"/g, '');
          mustClauses.push({
            term: { [field]: cleanValue }
          });
        }
      });
    }

    // Build final query
    const boolQuery = {
      bool: {
        filter: filters
      }
    };

    if (mustClauses.length > 0) {
      boolQuery.bool.must = mustClauses;
    }

    if (shouldClauses.length > 0) {
      boolQuery.bool.should = shouldClauses;
      boolQuery.bool.minimum_should_match = 1;
    }

    return boolQuery;
  }
}

// Create a singleton instance
export const logProcessor = new LogProcessor();

/**
 * Advanced Search Manager for OpenSearch
 */
export class AdvancedSearchManager {
  constructor() {
    this.dropdown = null;
    this.toggle = null;
    this.isOpen = false;
  }

  /**
   * Initialize advanced search functionality
   */
  init() {
    console.log('Initializing Advanced Search Manager...');

    // Get DOM elements
    this.dropdown = document.getElementById('advancedSearchDropdown');
    this.toggle = document.getElementById('advancedSearchToggle');

    if (!this.dropdown || !this.toggle) {
      console.warn('Advanced search elements not found');
      return;
    }

    // Set up event listeners
    this.setupEventListeners();

    console.log('Advanced Search Manager initialized');
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Toggle dropdown
    this.toggle.addEventListener('click', (e) => {
      e.preventDefault();
      this.toggleDropdown();
    });

    // Add input event listeners for field highlighting
    this.addFieldHighlighting();

    // Override main search button to handle advanced search
    const mainSearchButton = document.getElementById('opensearchSearchButton');
    if (mainSearchButton) {
      // Store original click handler
      const originalHandler = mainSearchButton.onclick;

      mainSearchButton.addEventListener('click', (e) => {
        if (this.hasAdvancedFields()) {
          e.preventDefault();
          e.stopPropagation();
          this.executeAdvancedSearch();
        }
        // If no advanced fields, let the normal search proceed
      });
    }
  }

  /**
   * Toggle the advanced search dropdown
   */
  toggleDropdown() {
    this.isOpen = !this.isOpen;

    if (this.isOpen) {
      this.dropdown.style.display = 'block';
      this.toggle.innerHTML = '<i class="bi bi-chevron-up me-1"></i>Advanced Search';
    } else {
      this.dropdown.style.display = 'none';
      this.toggle.innerHTML = '<i class="bi bi-chevron-down me-1"></i>Advanced Search';
    }
  }

  /**
   * Add field highlighting functionality
   */
  addFieldHighlighting() {
    const fields = ['advMessageId', 'advUri', 'advStatusCode', 'advClientId', 'advAppName', 'advMessageBody'];

    fields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        field.addEventListener('input', () => {
          // The CSS will handle the visual highlighting automatically
          // based on the :not(:placeholder-shown) selector
        });
      }
    });
  }

  /**
   * Check if any advanced search fields have values
   */
  hasAdvancedFields() {
    const fields = ['advMessageId', 'advUri', 'advStatusCode', 'advClientId', 'advAppName', 'advMessageBody'];

    return fields.some(fieldId => {
      const field = document.getElementById(fieldId);
      return field && field.value.trim() !== '';
    });
  }

  /**
   * Execute advanced search
   */
  async executeAdvancedSearch() {
    const query = this.buildAdvancedQuery();

    if (!query) {
      this.showErrorNotification('Please fill in at least one search field');
      return;
    }

    // Use main search configuration (index pattern and time range)
    const indexPattern = document.getElementById('opensearchIndexPattern')?.value || 'apigee-prod-*';

    // Get time range from the new TimeRangeManager
    let timeRange = '1d'; // fallback
    try {
      const { timeRangeManager } = await import('./time-range.js');
      const currentRange = timeRangeManager.getCurrentRange();
      timeRange = `${currentRange.from}|${currentRange.to}`;
    } catch (error) {
      console.warn('Could not get time range from TimeRangeManager, using fallback:', error);
    }

    console.log(`Advanced search: Executing unified search with index: ${indexPattern}, time range: ${timeRange}`);

    // Show loading indicator
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
      loadingOverlay.classList.remove('d-none');
    }

    // Execute search using the main search infrastructure
    try {
      // Import the required modules
      const { logProcessor } = await import('./processor.js');

      // Get search result count
      const resultCountSelect = document.getElementById('searchResultCount');
      const resultCount = resultCountSelect ? parseInt(resultCountSelect.value) : 500;

      // Execute the search with the main search configuration
      await logProcessor.processOpenSearchQuery(query, {
        timeRange: timeRange,
        size: resultCount
      });

    } catch (error) {
      console.error('Advanced search failed:', error);
      this.showErrorNotification('Search failed: ' + error.message);
    } finally {
      // Hide loading indicator
      if (loadingOverlay) {
        loadingOverlay.classList.add('d-none');
      }
    }
  }

  /**
   * Build advanced search query
   * @returns {string} OpenSearch query string
   */
  buildAdvancedQuery() {
    const fields = {
      messageId: document.getElementById('advMessageId')?.value?.trim(),
      uri: document.getElementById('advUri')?.value?.trim(),
      statusCode: document.getElementById('advStatusCode')?.value?.trim(),
      messageBody: document.getElementById('advMessageBody')?.value?.trim(),
      clientId: document.getElementById('advClientId')?.value?.trim(),
      appName: document.getElementById('advAppName')?.value?.trim()
    };

    const options = {
      operator: document.getElementById('advOperator')?.value || 'AND',
      indexPattern: document.getElementById('advIndexPattern')?.value || 'apigee-prod-*',
      timeRange: document.getElementById('advTimeRange')?.value || '1d'
    };

    // Build query parts
    const queryParts = [];

    // Field-specific queries
    if (fields.messageId) {
      // Use exact matching for message IDs - only use the main field (no .keyword variant exists)
      queryParts.push(`trace.messageid:"${fields.messageId}"`);
    }

    if (fields.uri) {
      // Always use wildcard search for URIs with the correct field name
      queryParts.push(`trace.uri:*${fields.uri}*`);
    }

    if (fields.statusCode) {
      queryParts.push(`trace.statuscode:${fields.statusCode}`);
    }

    if (fields.clientId) {
      queryParts.push(`trace.client_id:*${fields.clientId}*`);
    }

    if (fields.appName) {
      queryParts.push(`trace.app_name:*${fields.appName}*`);
    }

    // Message body search (searches in message and event.original fields)
    if (fields.messageBody) {
      queryParts.push(`(message:*${fields.messageBody}* OR event.original:*${fields.messageBody}*)`);
    }

    // Join with selected operator
    const query = queryParts.join(` ${options.operator} `);

    console.log('Advanced search query:', query);
    return query;
  }

  /**
   * Show error notification using app toast
   */
  showErrorNotification(message) {
    const toastElement = document.getElementById('liveToast');
    const toastBody = document.getElementById('toastMessage');

    if (toastElement && toastBody) {
      toastBody.textContent = message;

      // Set error style (red border)
      toastElement.classList.remove('border-success');
      toastElement.classList.add('border-danger');

      const toast = new bootstrap.Toast(toastElement);
      toast.show();
    } else {
      console.error(message);
    }
  }

  /**
   * Escape special characters for OpenSearch query string
   * @param {string} text - Text to escape
   * @returns {string} Escaped text
   */
  escapeSpecialChars(text) {
    // OpenSearch special characters that need escaping in query_string queries
    // + - = && || > < ! ( ) { } [ ] ^ " ~ * ? : \ /
    return text.replace(/[+\-=&|><!(){}[\]^"~*?:\\\/]/g, '\\$&');
  }

  /**
   * Clear the advanced search form
   */
  clearForm() {
    // Clear all input fields
    const inputs = [
      'advMessageId', 'advUri', 'advStatusCode', 'advMessageBody', 
      'advClientId', 'advAppName'
    ];

    inputs.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.value = '';
      }
    });

    // No select elements to reset in the new simplified design

    console.log('Advanced search form cleared');
  }

  /**
   * Populate form from existing query (if possible)
   * @param {string} query - Existing query to parse
   */
  populateFromQuery(query) {
    // This could be implemented to parse existing queries
    // and populate the form fields accordingly
    console.log('Populating form from query:', query);
  }
}

/**
 * Clear individual advanced search field
 * @param {string} fieldId - ID of the field to clear
 */
window.clearAdvancedField = function(fieldId) {
  const element = document.getElementById(fieldId);
  if (element) {
    element.value = '';
    element.focus();
  }
};

// Create and export singleton instance
export const advancedSearchManager = new AdvancedSearchManager();

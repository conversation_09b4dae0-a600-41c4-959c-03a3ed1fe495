# OpenSearch API Gateway - Testing Guide

## Testing on Windows (Development)

### Prerequisites
- Node.js 14+ installed
- NPM available
- Access to your OpenSearch server

### Quick Test
```batch
# Run the test script
test-gateway-windows.bat
```

### Manual Testing Steps

1. **Start the API Gateway**
   ```batch
   cd server
   npm install
   npm start
   ```

2. **Open Browser**
   - Go to: `http://localhost:3001`

3. **Configure OpenSearch**
   - Switch to "OpenSearch Mode"
   - Click "Configure" button
   - Enable "Use API Gateway" checkbox
   - Enter your credentials:
     - Endpoint: `https://bslogmesprod1.hu:9200`
     - Username: `[your username]`
     - Password: `[your password]`
   - Click "Test Connection"

4. **Verify Connection**
   - Should see "✅ Connection successful!"
   - Check browser console for detailed logs

### Testing API Gateway Health

Open browser console and run:
```javascript
// Check if gateway is running
fetch('http://localhost:3001/health')
  .then(r => r.json())
  .then(console.log);

// Test gateway configuration
opensearchClient.checkGatewayHealth()
  .then(console.log);
```

## Deployment to Production (No Sudo)

### Step 1: Create Deployment Package
```batch
# On Windows development machine
create-deployment-package.bat
```

### Step 2: Transfer to Production Server
```bash
# Using SCP
scp -r deployment-package user@your-server:/home/<USER>/

# Or use SFTP tools like WinSCP, FileZilla
```

### Step 3: Deploy on Production Server
```bash
# On the production server
cd deployment-package
chmod +x *.sh
./deploy-production-no-sudo.sh
```

### Step 4: Start the Service
```bash
cd ~/opensearch-gateway/server

# Interactive mode (for testing)
./start-gateway.sh

# Background mode (for production)
./start-gateway-background.sh
```

### Step 5: Verify Production Deployment
```bash
# Check status
./status-gateway.sh

# View logs
tail -f ../logs/gateway.log

# Test health endpoint
curl http://localhost:3001/health
```

## Production Management (No Sudo Required)

### Service Management
```bash
cd ~/opensearch-gateway/server

# Start service
./start-gateway-background.sh

# Stop service
./stop-gateway.sh

# Check status
./status-gateway.sh

# View logs
tail -f ../logs/gateway.log
```

### Monitoring
```bash
# Check if process is running
pgrep -f "opensearch-gateway.js"

# Check port usage
netstat -tuln | grep :3001

# Monitor resource usage
top -p $(pgrep -f "opensearch-gateway.js")
```

### Troubleshooting

**Gateway won't start:**
```bash
# Check Node.js version
node --version  # Should be 14+

# Check dependencies
cd ~/opensearch-gateway/server
npm install

# Check logs
cat ../logs/gateway.log
```

**Connection issues:**
```bash
# Test local connectivity
curl http://localhost:3001/health

# Check if port is blocked
telnet localhost 3001

# Check firewall (if you have access)
firewall-cmd --list-ports
```

**OpenSearch connection fails:**
```bash
# Test from server directly
curl -k -u username:password https://bslogmesprod1.hu:9200

# Check network connectivity
ping bslogmesprod1.hu
```

## Testing Different Scenarios

### 1. Direct Connection vs Gateway
```javascript
// Test direct connection (should fail with CORS)
opensearchClient.configureGateway(false);
opensearchClient.testConnection();

// Test gateway connection (should work)
opensearchClient.configureGateway(true, 'http://localhost:3001');
opensearchClient.testConnection();
```

### 2. Different Gateway URLs
```javascript
// Local development
opensearchClient.configureGateway(true, 'http://localhost:3001');

// Production server
opensearchClient.configureGateway(true, 'http://your-server:3001');
```

### 3. Error Handling
```javascript
// Test with wrong gateway URL
opensearchClient.configureGateway(true, 'http://localhost:9999');
opensearchClient.checkGatewayHealth().then(console.log);

// Test with wrong credentials
opensearchClient.updateConfig({
  username: 'wrong',
  password: 'wrong'
});
opensearchClient.testConnection().then(console.log);
```

## Performance Testing

### Load Testing (Optional)
```bash
# Install Apache Bench (if available)
ab -n 100 -c 10 http://localhost:3001/health

# Or use curl in a loop
for i in {1..10}; do
  curl -s http://localhost:3001/health > /dev/null
  echo "Request $i completed"
done
```

### Memory Monitoring
```bash
# Monitor memory usage
watch -n 5 'ps -p $(pgrep -f "opensearch-gateway.js") -o pid,ppid,%mem,%cpu,cmd'
```

## Security Testing

### 1. Credential Protection
- Verify credentials are not visible in browser network tab
- Check that credentials are not logged in browser console
- Confirm credentials are only sent to gateway, not OpenSearch directly

### 2. CORS Verification
- Confirm no CORS errors in browser console
- Test from different origins (if applicable)

### 3. Error Information
- Verify sensitive information is not exposed in error messages
- Check that stack traces don't reveal internal details

This testing approach ensures your OpenSearch API Gateway works correctly in both development and production environments without requiring sudo access.

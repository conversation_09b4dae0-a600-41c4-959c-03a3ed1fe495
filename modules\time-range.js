/**
 * OpenSearch-style Time Range Manager with From/To fields
 */
export class TimeRangeManager {
  constructor() {
    this.fromDate = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago
    this.toDate = new Date(); // now
    this.fromMode = 'absolute'; // absolute, relative, now
    this.toMode = 'now'; // absolute, relative, now
    this.openDropdown = null; // 'from' or 'to' or null
    this.fromSelectedLabel = 'Last 30 minutes'; // Default to Last 30 minutes
  }

  init() {
    console.log('Initializing OpenSearch-style Time Range Manager...');

    // Wait a bit for DOM to be fully ready
    setTimeout(() => {
      this.doInit();
    }, 100);
  }

  doInit() {
    // Get DOM elements
    this.fromButton = document.getElementById('fromTimeButton');
    this.toButton = document.getElementById('toTimeButton');
    this.fromMenu = document.getElementById('fromTimeMenu');
    this.toMenu = document.getElementById('toTimeMenu');
    this.fromDisplay = document.getElementById('fromTimeDisplay');
    this.toDisplay = document.getElementById('toTimeDisplay');

    console.log('Time range elements found:', {
      fromButton: !!this.fromButton,
      toButton: !!this.toButton,
      fromMenu: !!this.fromMenu,
      toMenu: !!this.toMenu,
      fromDisplay: !!this.fromDisplay,
      toDisplay: !!this.toDisplay
    });

    if (!this.fromButton || !this.toButton || !this.fromMenu || !this.toMenu) {
      console.warn('Time range elements not found');
      return;
    }

    this.setupEventListeners();
    this.initializeCalendars();
    this.updateDisplays();

    console.log('OpenSearch-style Time Range Manager initialized');
  }

  setupEventListeners() {
    // From button click
    this.fromButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('From time button clicked');
      this.toggleDropdown('from');
    });

    // To button click
    this.toButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('To time button clicked');
      this.toggleDropdown('to');
    });

    // Tab clicks
    document.querySelectorAll('.time-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const tabType = tab.dataset.tab;
        const field = tab.dataset.field;
        console.log(`Tab clicked: ${tabType} for ${field}`);
        this.switchTab(field, tabType);
      });
    });

    // Set buttons for absolute time
    document.getElementById('fromSetAbsolute')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.setAbsoluteTime('from');
    });

    document.getElementById('toSetAbsolute')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.setAbsoluteTime('to');
    });

    // Set buttons for relative time
    document.getElementById('fromSetRelative')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.setRelativeTime('from');
    });

    // Set to now buttons
    document.getElementById('toSetNow')?.addEventListener('click', (e) => {
      e.preventDefault();
      this.setToNow('to');
    });

    // Commonly used options
    document.querySelectorAll('.commonly-used-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const value = parseInt(btn.dataset.value);
        const unit = btn.dataset.unit;
        const label = btn.dataset.label;
        this.setCommonlyUsed(value, unit, label);
      });
    });

    // Round to minute checkbox
    document.getElementById('fromRoundToMinute')?.addEventListener('change', () => {
      this.updateRelativeDisplays();
    });

    // Relative value/unit changes
    document.getElementById('fromRelativeValue')?.addEventListener('input', () => {
      this.updateRelativeDisplays();
    });

    document.getElementById('fromRelativeUnit')?.addEventListener('change', () => {
      this.updateRelativeDisplays();
    });

    // Relative inputs
    ['fromRelativeValue', 'fromRelativeUnit', 'toRelativeValue', 'toRelativeUnit'].forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => this.updateRelativeDisplays());
        element.addEventListener('input', () => this.updateRelativeDisplays());
      }
    });

    // Date and time pickers
    ['fromDatePicker', 'fromTimePicker', 'toDatePicker', 'toTimePicker'].forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => this.updateAbsoluteDates());
      }
    });

    // Click outside to close
    document.addEventListener('click', (e) => {
      if (!this.fromButton.contains(e.target) && !this.fromMenu.contains(e.target) &&
          !this.toButton.contains(e.target) && !this.toMenu.contains(e.target)) {
        this.closeAllDropdowns();
      }
    });

    // Prevent dropdown menu clicks from closing
    [this.fromMenu, this.toMenu].forEach(menu => {
      menu.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    });

    // Handle window resize and scroll to reposition dropdowns
    window.addEventListener('resize', () => this.repositionOpenDropdown());
    window.addEventListener('scroll', () => this.repositionOpenDropdown());
  }

  toggleDropdown(field) {
    if (this.openDropdown === field) {
      this.closeAllDropdowns();
    } else {
      this.closeAllDropdowns();
      this.openDropdown = field;
      const menu = field === 'from' ? this.fromMenu : this.toMenu;
      const button = field === 'from' ? this.fromButton : this.toButton;

      // Calculate position for fixed positioning
      const buttonRect = button.getBoundingClientRect();
      menu.style.top = (buttonRect.bottom + 2) + 'px';
      menu.style.left = buttonRect.left + 'px';
      menu.style.display = 'block';

      console.log(`Opened ${field} dropdown at position: top=${buttonRect.bottom + 2}px, left=${buttonRect.left}px`);
    }
  }

  closeAllDropdowns() {
    this.fromMenu.style.display = 'none';
    this.toMenu.style.display = 'none';
    this.openDropdown = null;
    console.log('Closed all dropdowns');
  }

  repositionOpenDropdown() {
    if (this.openDropdown) {
      const menu = this.openDropdown === 'from' ? this.fromMenu : this.toMenu;
      const button = this.openDropdown === 'from' ? this.fromButton : this.toButton;

      // Recalculate position
      const buttonRect = button.getBoundingClientRect();
      menu.style.top = (buttonRect.bottom + 2) + 'px';
      menu.style.left = buttonRect.left + 'px';
    }
  }

  switchTab(field, tabType) {
    // Only update UI, don't change mode until Set button is clicked
    const container = field === 'from' ? this.fromMenu : this.toMenu;
    container.querySelectorAll('.time-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    container.querySelectorAll('.tab-pane').forEach(pane => {
      pane.classList.remove('active');
    });

    container.querySelector(`[data-tab="${tabType}"]`).classList.add('active');
    container.querySelector(`#${field}-${tabType}`).classList.add('active');

    console.log(`Switched ${field} to ${tabType} tab (UI only)`);
  }

  setToNow(field) {
    const now = new Date();
    if (field === 'from') {
      this.fromDate = now;
      this.fromMode = 'now';
      this.fromSelectedLabel = null; // Clear selected label
    } else {
      this.toDate = now;
      this.toMode = 'now';
    }
    this.updateDisplays();
    this.closeAllDropdowns();
    console.log(`Set ${field} to now`);
  }

  applyTimeRange() {
    // Calculate final dates based on current modes
    this.calculateFinalDates();

    // Update displays
    this.updateDisplays();

    // Close dropdowns
    this.closeAllDropdowns();

    // Update hidden inputs for form submission
    const fromInput = document.getElementById('timeRangeFrom');
    const toInput = document.getElementById('timeRangeTo');
    const rangeInput = document.getElementById('timeRange');

    if (fromInput) fromInput.value = this.fromDate.toISOString();
    if (toInput) toInput.value = this.toDate.toISOString();
    if (rangeInput) rangeInput.value = `${this.fromDate.toISOString()}|${this.toDate.toISOString()}`;

    console.log('Applied time range:', {
      from: this.fromDate.toISOString(),
      to: this.toDate.toISOString(),
      fromMode: this.fromMode,
      toMode: this.toMode
    });
  }

  calculateFinalDates() {
    const now = new Date();

    // Calculate from date
    if (this.fromMode === 'now') {
      this.fromDate = new Date(now);
    } else if (this.fromMode === 'relative') {
      const value = parseInt(document.getElementById('fromRelativeValue')?.value || '15');
      const unit = document.getElementById('fromRelativeUnit')?.value || 'minutes';
      this.fromDate = this.subtractTime(now, value, unit);
    }
    // For absolute mode, fromDate is already set by calendar

    // Calculate to date
    if (this.toMode === 'now') {
      this.toDate = new Date(now);
    } else if (this.toMode === 'relative') {
      const value = parseInt(document.getElementById('toRelativeValue')?.value || '0');
      const unit = document.getElementById('toRelativeUnit')?.value || 'minutes';
      this.toDate = this.subtractTime(now, value, unit);
    }
    // For absolute mode, toDate is already set by calendar
  }

  subtractTime(date, value, unit) {
    const result = new Date(date);
    switch (unit) {
      case 'minutes':
        result.setMinutes(result.getMinutes() - value);
        break;
      case 'hours':
        result.setHours(result.getHours() - value);
        break;
      case 'days':
        result.setDate(result.getDate() - value);
        break;
      case 'weeks':
        result.setDate(result.getDate() - (value * 7));
        break;
      case 'months':
        result.setMonth(result.getMonth() - value);
        break;
    }
    return result;
  }

  updateDisplays() {
    // Update from display
    let fromText;
    if (this.fromMode === 'now') {
      fromText = 'now';
    } else if (this.fromMode === 'relative') {
      const value = document.getElementById('fromRelativeValue')?.value || '15';
      const unit = document.getElementById('fromRelativeUnit')?.value || 'minutes';
      fromText = `${value} ${unit} ago`;
    } else {
      // Show selected label if available, otherwise show formatted date
      fromText = this.fromSelectedLabel || this.formatDisplayDate(this.fromDate);
    }

    // Update to display
    let toText;
    if (this.toMode === 'now') {
      toText = 'now';
    } else if (this.toMode === 'relative') {
      const value = document.getElementById('toRelativeValue')?.value || '0';
      const unit = document.getElementById('toRelativeUnit')?.value || 'minutes';
      toText = value === '0' ? 'now' : `${value} ${unit} ago`;
    } else {
      toText = this.formatDisplayDate(this.toDate);
    }

    if (this.fromDisplay) this.fromDisplay.textContent = fromText;
    if (this.toDisplay) this.toDisplay.textContent = toText;

    // Update internal displays in dropdowns
    this.updateRelativeDisplays();
    this.updateAbsoluteDisplays();
  }

  updateRelativeDisplays() {
    // Update from relative display
    const fromValue = document.getElementById('fromRelativeValue')?.value || '15';
    const fromUnit = document.getElementById('fromRelativeUnit')?.value || 'minutes';
    const fromRoundToMinute = document.getElementById('fromRoundToMinute')?.checked || false;
    const fromRelativeDisplay = document.getElementById('fromRelativeDisplay');
    if (fromRelativeDisplay) {
      let fromDate = this.subtractTime(new Date(), parseInt(fromValue), fromUnit);

      // Round to minute if checkbox is checked
      if (fromRoundToMinute) {
        fromDate.setSeconds(0);
        fromDate.setMilliseconds(0);
      }

      fromRelativeDisplay.textContent = this.formatDisplayDate(fromDate);
    }

    // Update to relative display
    const toValue = document.getElementById('toRelativeValue')?.value || '0';
    const toUnit = document.getElementById('toRelativeUnit')?.value || 'minutes';
    const toRelativeDisplay = document.getElementById('toRelativeDisplay');
    if (toRelativeDisplay) {
      if (toValue === '0') {
        toRelativeDisplay.textContent = 'now';
      } else {
        const toDate = this.subtractTime(new Date(), parseInt(toValue), toUnit);
        toRelativeDisplay.textContent = this.formatDisplayDate(toDate);
      }
    }
  }

  updateAbsoluteDisplays() {
    // Update from absolute display
    const fromDateDisplay = document.getElementById('fromDateDisplay');
    if (fromDateDisplay) {
      fromDateDisplay.textContent = this.formatDisplayDate(this.fromDate);
    }

    // Update to absolute display
    const toDateDisplay = document.getElementById('toDateDisplay');
    if (toDateDisplay) {
      toDateDisplay.textContent = this.formatDisplayDate(this.toDate);
    }
  }

  initializeCalendars() {
    // Set date pickers
    const fromDatePicker = document.getElementById('fromDatePicker');
    const toDatePicker = document.getElementById('toDatePicker');
    const fromTimePicker = document.getElementById('fromTimePicker');
    const toTimePicker = document.getElementById('toTimePicker');

    if (fromDatePicker) {
      fromDatePicker.value = this.formatDateForInput(this.fromDate);
    }

    if (toDatePicker) {
      toDatePicker.value = this.formatDateForInput(this.toDate);
    }

    if (fromTimePicker) {
      fromTimePicker.value = this.formatTimeForInput(this.fromDate);
    }

    if (toTimePicker) {
      toTimePicker.value = this.formatTimeForInput(this.toDate);
    }
  }

  updateAbsoluteDates() {
    // Update from date from inputs
    const fromDatePicker = document.getElementById('fromDatePicker');
    const fromTimePicker = document.getElementById('fromTimePicker');

    if (fromDatePicker && fromTimePicker && fromDatePicker.value && fromTimePicker.value) {
      const dateStr = fromDatePicker.value;
      const timeStr = fromTimePicker.value;
      this.fromDate = new Date(`${dateStr}T${timeStr}`);
    }

    // Update to date from inputs
    const toDatePicker = document.getElementById('toDatePicker');
    const toTimePicker = document.getElementById('toTimePicker');

    if (toDatePicker && toTimePicker && toDatePicker.value && toTimePicker.value) {
      const dateStr = toDatePicker.value;
      const timeStr = toTimePicker.value;
      this.toDate = new Date(`${dateStr}T${timeStr}`);
    }

    // Update displays
    this.updateAbsoluteDisplays();
  }

  formatDisplayDate(date) {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }) + ' @ ' + date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }) + '.' + String(date.getMilliseconds()).padStart(3, '0');
  }

  formatTimeForInput(date) {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  }

  formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  updateRelativeDisplay() {
    const prefix = document.getElementById('relativePrefix')?.value || 'Last';
    const value = document.getElementById('relativeValue')?.value || '15';
    const unit = document.getElementById('relativeUnit')?.value || 'minutes';

    if (value && parseInt(value) > 0) {
      const display = `${prefix} ${value} ${unit}`;
      const fromTo = `${value} ${unit} ago → now`;

      // Update the from/to display
      if (this.fromToDisplay) {
        this.fromToDisplay.textContent = fromTo;
      }
    }
  }

  updateAbsoluteDisplay() {
    const fromDate = document.getElementById('absoluteFromDate')?.value;
    const fromTime = document.getElementById('absoluteFromTime')?.value;
    const toDate = document.getElementById('absoluteToDate')?.value;
    const toTime = document.getElementById('absoluteToTime')?.value;

    if (fromDate && fromTime && toDate && toTime) {
      const fromDateTime = new Date(`${fromDate}T${fromTime}`);
      const toDateTime = new Date(`${toDate}T${toTime}`);

      const fromTo = `${this.formatDateTime(fromDateTime)} → ${this.formatDateTime(toDateTime)}`;

      if (this.fromToDisplay) {
        this.fromToDisplay.textContent = fromTo;
      }
    }
  }

  updateDisplayForCurrentTab() {
    const activeTab = document.querySelector('#timeRangeTabs .nav-link.active');
    if (!activeTab) return;

    if (activeTab.id === 'relative-tab') {
      this.updateRelativeDisplay();
    } else if (activeTab.id === 'absolute-tab') {
      this.updateAbsoluteDisplay();
    } else if (activeTab.id === 'now-tab') {
      if (this.fromToDisplay) {
        this.fromToDisplay.textContent = 'now';
      }
    }
  }

  applyCurrentSettings() {
    const activeTab = document.querySelector('#timeRangeTabs .nav-link.active');
    if (!activeTab) return;

    if (activeTab.id === 'relative-tab') {
      this.applyRelativeTime();
    } else if (activeTab.id === 'absolute-tab') {
      this.applyAbsoluteTime();
    } else if (activeTab.id === 'now-tab') {
      this.setToNow();
    }
  }

  applyRelativeTime() {
    const prefix = document.getElementById('relativePrefix')?.value || 'Last';
    const value = document.getElementById('relativeValue')?.value;
    const unit = document.getElementById('relativeUnit')?.value || 'minutes';

    if (!value || parseInt(value) <= 0) {
      alert('Please enter a valid time value');
      return;
    }

    // Convert to short format for value
    const unitMap = {
      'minutes': 'm',
      'hours': 'h',
      'days': 'd',
      'weeks': 'w',
      'months': 'M',
      'years': 'y'
    };
    const shortUnit = unitMap[unit] || unit;
    const timeRange = `${value}${shortUnit}`;
    const display = `${prefix} ${value} ${unit}`;
    const fromTo = `${value} ${unit} ago → now`;

    this.setTimeRange(timeRange, display, fromTo);
  }

  applyAbsoluteTime() {
    const fromDate = document.getElementById('absoluteFromDate')?.value;
    const fromTime = document.getElementById('absoluteFromTime')?.value;
    const toDate = document.getElementById('absoluteToDate')?.value;
    const toTime = document.getElementById('absoluteToTime')?.value;

    if (!fromDate || !fromTime || !toDate || !toTime) {
      alert('Please select both from and to date/times');
      return;
    }

    const fromDateTime = new Date(`${fromDate}T${fromTime}`);
    const toDateTime = new Date(`${toDate}T${toTime}`);

    if (fromDateTime >= toDateTime) {
      alert('From time must be before to time');
      return;
    }

    // Convert to ISO strings for OpenSearch
    const timeRange = `${fromDateTime.toISOString()}|${toDateTime.toISOString()}`;
    const display = `${this.formatDate(fromDateTime)} to ${this.formatDate(toDateTime)}`;
    const fromTo = `${this.formatDateTime(fromDateTime)} → ${this.formatDateTime(toDateTime)}`;

    this.setTimeRange(timeRange, display, fromTo);
  }



  getCurrentFromDate() {
    return this.fromDate;
  }

  getCurrentToDate() {
    return this.toDate;
  }

  setAbsoluteTime(field) {
    const datePicker = document.getElementById(`${field}DatePicker`);
    const timePicker = document.getElementById(`${field}TimePicker`);

    if (datePicker.value && timePicker.value) {
      const dateTime = new Date(`${datePicker.value}T${timePicker.value}`);

      if (field === 'from') {
        this.fromDate = dateTime;
        this.fromMode = 'absolute';
        this.fromSelectedLabel = null; // Clear selected label when setting manual absolute time
      } else {
        this.toDate = dateTime;
        this.toMode = 'absolute';
      }

      // Check if to date is earlier than from date
      if (this.fromMode === 'absolute' && this.toMode === 'absolute' && this.toDate < this.fromDate) {
        this.showDateRangeWarning();
        return; // Don't proceed with invalid date range
      } else {
        this.clearDateRangeWarning();
      }

      this.updateDisplays();
      this.closeAllDropdowns();
      console.log(`Set ${field} to absolute time:`, dateTime);
    }
  }

  setRelativeTime(field) {
    if (field === 'from') {
      const valueInput = document.getElementById('fromRelativeValue');
      const unitSelect = document.getElementById('fromRelativeUnit');
      const roundToMinute = document.getElementById('fromRoundToMinute');

      const value = parseInt(valueInput.value);
      const unit = unitSelect.value;
      const shouldRound = roundToMinute.checked;

      const now = new Date();
      const multiplier = {
        'minutes': 60 * 1000,
        'hours': 60 * 60 * 1000,
        'days': 24 * 60 * 60 * 1000,
        'weeks': 7 * 24 * 60 * 60 * 1000,
        'months': 30 * 24 * 60 * 60 * 1000
      };

      let fromDate = new Date(now.getTime() - (value * multiplier[unit]));

      // Round to minute if checkbox is checked
      if (shouldRound) {
        fromDate.setSeconds(0);
        fromDate.setMilliseconds(0);
      }

      this.fromDate = fromDate;
      this.fromMode = 'relative';
      this.fromSelectedLabel = `${value} ${unit} ago${shouldRound ? ' (rounded)' : ''}`;
      this.updateDisplays();
      this.closeAllDropdowns();
      console.log('Set from to relative time:', { value, unit, shouldRound, fromDate });
    }
  }

  setCommonlyUsed(value, unit, label) {
    const now = new Date();
    let fromDate;

    if (unit === 'minutes' && value === 0) {
      // Today - start of today
      fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    } else if (unit === 'week' && value === 0) {
      // This week - start of this week (Monday)
      const dayOfWeek = now.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - daysToMonday);
    } else {
      // Calculate relative time
      const multiplier = {
        'minutes': 60 * 1000,
        'hours': 60 * 60 * 1000,
        'days': 24 * 60 * 60 * 1000
      };

      fromDate = new Date(now.getTime() - (value * multiplier[unit]));
    }

    this.fromDate = fromDate;
    this.fromMode = 'absolute';
    this.fromSelectedLabel = label; // Store the selected label
    this.toDate = now;
    this.toMode = 'now';

    this.updateDisplays();
    this.closeAllDropdowns();
    console.log(`Set commonly used range: ${label}`, { from: fromDate, to: now });
  }

  showDateRangeWarning() {
    // Show custom notification using Bootstrap toast
    const toastElement = document.getElementById('liveToast');
    const toastBody = document.getElementById('toastMessage');

    if (toastElement && toastBody) {
      toastBody.textContent = 'Warning: End date is earlier than start date. Please check your time range.';

      // Set warning style (red border)
      toastElement.classList.remove('border-success');
      toastElement.classList.add('border-danger');

      const toast = new bootstrap.Toast(toastElement);
      toast.show();
    } else {
      console.warn('Warning: End date is earlier than start date. Please check your time range.');
    }

    // Add red border to date inputs
    const fromDatePicker = document.getElementById('fromDatePicker');
    const fromTimePicker = document.getElementById('fromTimePicker');
    const toDatePicker = document.getElementById('toDatePicker');
    const toTimePicker = document.getElementById('toTimePicker');

    [fromDatePicker, fromTimePicker, toDatePicker, toTimePicker].forEach(input => {
      if (input) {
        input.style.borderColor = '#dc3545';
        input.style.borderWidth = '2px';
      }
    });
  }

  clearDateRangeWarning() {
    // Remove red border from date inputs
    const fromDatePicker = document.getElementById('fromDatePicker');
    const fromTimePicker = document.getElementById('fromTimePicker');
    const toDatePicker = document.getElementById('toDatePicker');
    const toTimePicker = document.getElementById('toTimePicker');

    [fromDatePicker, fromTimePicker, toDatePicker, toTimePicker].forEach(input => {
      if (input) {
        input.style.borderColor = '';
        input.style.borderWidth = '';
      }
    });
  }

  getCurrentRange() {
    // Always recalculate dates for relative/now modes to ensure current time
    this.calculateFinalDates();

    return {
      from: this.fromDate.toISOString(),
      to: this.toDate.toISOString(),
      fromMode: this.fromMode,
      toMode: this.toMode
    };
  }
}

// Create and export singleton instance
export const timeRangeManager = new TimeRangeManager();
